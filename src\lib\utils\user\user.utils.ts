// src/lib/utils/user.utils.ts
import type { DisplayUserProfile } from '$lib/schemas/profile.schema';
import type z from 'zod';
import { type ProfileForScore } from '$lib/schemas/profile.schema'; // ✅ 导入精确的类型

const MIN_BIO_LENGTH_FOR_BONUS = 10;
const MIN_RATINGS_FOR_BONUS = 1;

// ✅ 1. 定义一个计分权重对象，所有规则一目了然
const scoreWeights: { [K in keyof ProfileForScore]?: (value: any) => number } = {
	nickname: (val) => (val ? 10 : 0),
	age: (val) => (val ? 5 : 0),
	heightCm: (val) => (val ? 5 : 0),
	weightKg: (val) => (val ? 5 : 0),
	bodyType: (val) => (val && val !== 'prefer_not_to_say' ? 10 : val ? 5 : 0),
	countryCode: (val) => (val ? 10 : 0),
	orientation: (val) => (val && val !== 'prefer_not_to_say' ? 10 : val ? 5 : 0),
	presentationStyle: (val) => (val && val !== 'prefer_not_to_say' ? 10 : val ? 5 : 0),
	relationshipStatus: (val) => (val && val !== 'prefer_not_to_say' ? 10 : val ? 5 : 0),
	bio: (val) => {
		if (!val) return 0;
		return val.length > MIN_BIO_LENGTH_FOR_BONUS ? 10 : 5;
	},
	kinkCategoryBitmask: (val) => (val && val > 0n ? 10 : 0),
	kinkRatings: (val) => (val && Object.keys(val).length >= MIN_RATINGS_FOR_BONUS ? 15 : 0)
};

/**
 * 计算用户资料的完整度分数。
 * @param profile - 一个符合 ProfileForScore schema 的用户资料对象。
 * @returns 资料完整度分数 (0-100)。
 */
export function calculateProfileCompleteness(profile: Partial<ProfileForScore>): number {
	if (!profile) return 0;

	let score = 0;
	// ✅ 2. 遍历权重对象，动态计算分数
	for (const key in scoreWeights) {
		const K = key as keyof ProfileForScore;
		const calculator = scoreWeights[K];
		if (calculator) {
			score += calculator(profile[K]);
		}
	}

	return Math.min(score, 100); // 确保分数不会超过100
}

export const ONBOARDING_THRESHOLD = 50;
/**
 * 判断用户是否需要强制进行初始 Onboarding。
 * 这通常用于新用户或资料极度不完整的用户。
 * @param user 用户资料对象。
 * @returns 如果需要强制 Onboarding，则返回 true，否则返回 false。
 */
export function isInitialOnboardingRequired(user: DisplayUserProfile): boolean {
	// 如果昵称缺失，或者资料完整度为0，则认为需要强制引导
	return !user.nickname || user.profileCompletenessScore < ONBOARDING_THRESHOLD;
}

/**
 * 从 Zod 对象 schema 中提取所有键，并创建一个 Drizzle 'columns' 配置对象。
 * @param schema - 一个 Zod 对象 schema
 * @returns 一个形如 { key1: true, key2: true, ... } 的对象
 */
export function getZodKeys<T extends z.ZodObject<any, any, any>>(
	schema: T
): { [K in keyof T['shape']]: true } {
	return Object.fromEntries(Object.keys(schema.shape).map((key) => [key, true])) as {
		[K in keyof T['shape']]: true;
	};
}