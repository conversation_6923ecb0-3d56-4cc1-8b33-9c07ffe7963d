// src/lib/server/bot/botManager.ts
// CipherX TMA - Telegram Bot 管理模块

import { Bot } from 'grammy';
import { env } from '$env/dynamic/private';
import { db } from '$lib/server/db';
import { bots } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Bot 实例缓存
 * 使用 Map 来缓存已创建的 Bot 实例以提升性能
 */
const botInstanceCache = new Map<string, Bot>();

/**
 * 管理员 ID 列表
 * 从环境变量中读取，支持多个管理员
 */
let adminIds: number[] = [];

/**
 * 初始化管理员 ID 列表
 */
function initializeAdminIds(): void {
	if (adminIds.length === 0) {
		const adminIdsStr = env.BOT_ADMIN_IDS;
		if (adminIdsStr) {
			adminIds = adminIdsStr.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
		}
	}
}

/**
 * 检查用户是否为管理员
 */
export function isAdmin(userId: number): boolean {
	initializeAdminIds();
	return adminIds.includes(userId);
}

/**
 * 根据 Token 别名获取真实的 Bot Token
 * 优先从数据库查询，如果没有则从环境变量查找
 */
async function getBotTokenByAlias(tokenAlias: string): Promise<string | null> {
	try {
		// 首先尝试从数据库查询
		const botRecord = await db
			.select({ telegramBotToken: bots.telegramBotToken })
			.from(bots)
			.where(eq(bots.tokenAlias, tokenAlias))
			.limit(1);

		if (botRecord.length > 0) {
			return botRecord[0].telegramBotToken;
		}

		// 如果数据库中没有，则从环境变量查找
		// 支持的环境变量格式: BOT_TOKEN_USA, BOT_TOKEN_CHINA 等
		const envKey = `BOT_TOKEN_${tokenAlias.toUpperCase().replace(/-/g, '_')}`;
		const token = env[envKey];
		
		if (token) {
			return token;
		}

		console.warn(`Bot token not found for alias: ${tokenAlias}`);
		return null;
	} catch (error) {
		console.error(`Error getting bot token for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 根据 Token 别名获取或创建 Bot 实例
 * 使用缓存机制避免重复创建实例
 */
export async function getBotInstance(tokenAlias: string): Promise<Bot | null> {
	try {
		// 检查缓存中是否已有实例
		if (botInstanceCache.has(tokenAlias)) {
			return botInstanceCache.get(tokenAlias)!;
		}

		// 获取 Bot Token
		const token = await getBotTokenByAlias(tokenAlias);
		if (!token) {
			return null;
		}

		// 创建新的 Bot 实例
		const bot = new Bot(token);

		// 设置基本的错误处理
		bot.catch((err) => {
			console.error(`Bot error for ${tokenAlias}:`, err);
		});

		// 缓存实例
		botInstanceCache.set(tokenAlias, bot);

		console.log(`Bot instance created and cached for alias: ${tokenAlias}`);
		return bot;
	} catch (error) {
		console.error(`Error creating bot instance for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 清除指定别名的 Bot 实例缓存
 * 用于重新加载 Bot 配置时
 */
export function clearBotCache(tokenAlias: string): void {
	if (botInstanceCache.has(tokenAlias)) {
		botInstanceCache.delete(tokenAlias);
		console.log(`Bot cache cleared for alias: ${tokenAlias}`);
	}
}

/**
 * 清除所有 Bot 实例缓存
 */
export function clearAllBotCache(): void {
	botInstanceCache.clear();
	console.log('All bot cache cleared');
}

/**
 * 获取当前缓存的 Bot 实例数量
 */
export function getCachedBotCount(): number {
	return botInstanceCache.size;
}

/**
 * 获取所有缓存的 Bot 别名列表
 */
export function getCachedBotAliases(): string[] {
	return Array.from(botInstanceCache.keys());
}

/**
 * 验证 Bot Token 格式
 */
export function validateBotToken(token: string): boolean {
	// Telegram Bot Token 格式: 数字:字母数字字符串
	const tokenRegex = /^\d+:[A-Za-z0-9_-]+$/;
	return tokenRegex.test(token);
}

/**
 * 从数据库获取所有活跃的 Bot 配置
 */
export async function getAllActiveBots() {
	try {
		return await db
			.select()
			.from(bots)
			.where(eq(bots.status, 'active'));
	} catch (error) {
		console.error('Error fetching active bots:', error);
		return [];
	}
}

/**
 * 预热 Bot 实例缓存
 * 在应用启动时调用，预先创建常用的 Bot 实例
 */
export async function warmupBotCache(): Promise<void> {
	try {
		const activeBots = await getAllActiveBots();
		
		for (const botConfig of activeBots) {
			await getBotInstance(botConfig.tokenAlias);
		}
		
		console.log(`Bot cache warmed up with ${activeBots.length} instances`);
	} catch (error) {
		console.error('Error warming up bot cache:', error);
	}
}
