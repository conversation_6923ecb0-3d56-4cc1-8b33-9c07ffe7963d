// src/lib/server/bot/botManager.ts
// CipherX TMA - Telegram Bot 管理模块

import { Bot } from 'grammy';
import { env } from '$env/dynamic/private';
import { db } from '$lib/server/db';
import { bots } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { autoRetry } from '@grammyjs/auto-retry';
const botInstanceCache = new Map<string, Bot>();
let adminIds: number[] = [];

function initializeAdminIds(): void {
	if (adminIds.length === 0) {
		const adminIdsStr = env.BOT_ADMIN_IDS;
		if (adminIdsStr) {
			adminIds = adminIdsStr
				.split(',')
				.map((id) => parseInt(id.trim(), 10))
				.filter((id) => !isNaN(id));
		}
	}
}

export function isAdmin(userId: number): boolean {
	initializeAdminIds();
	return adminIds.includes(userId);
}

/**
 * 根据 Token 别名获取真实的 Bot Token (最终安全版)
 * 1. 验证别名是否存在于数据库且状态为 active
 * 2. 如果合法，则从环境变量中读取真实 Token
 */
async function getBotTokenByAlias(tokenAlias: string): Promise<string | null> {
	try {
		// 步骤一：查询数据库，验证别名的合法性和状态
		const botRecord = await db.query.bots.findFirst({
			where: (bots, { and, eq }) => and(eq(bots.tokenAlias, tokenAlias), eq(bots.status, 'active'))
		});

		// 如果数据库中没有这个别名，或者它不是'active'状态，则认为它无效
		if (!botRecord) {
			console.warn(`Bot with alias "${tokenAlias}" is not found or not active in the database.`);
			return null;
		}

		// 步骤二：如果验证通过，才从环境变量中构造并读取Token
		const envKey = `BOT_TOKEN_${tokenAlias.toUpperCase().replace(/-/g, '_')}`;
		const token = env[envKey];

		// 如果在环境变量中找不到对应的Token，这是一个严重的配置错误
		if (!token) {
			console.error(
				`CRITICAL CONFIGURATION ERROR: Bot "${tokenAlias}" is active in the database, but its token is MISSING in the environment variables (expected to find it in a variable named '${envKey}').`
			);
			return null;
		}

		return token;
	} catch (error) {
		console.error(`Error during getBotTokenByAlias for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 根据 Token 别名获取或创建 Bot 实例
 * (此函数保持不变，因为它依赖的 getBotTokenByAlias 已被优化)
 */
export async function getBotInstance(tokenAlias: string): Promise<Bot | null> {
	try {
		if (botInstanceCache.has(tokenAlias)) {
			return botInstanceCache.get(tokenAlias)!;
		}

		const token = await getBotTokenByAlias(tokenAlias);
		if (!token) {
			return null;
		}

		const bot = new Bot(token);
		bot.api.config.use(autoRetry());

		bot.catch((err) => {
			console.error(`Bot error for ${tokenAlias}:`, err);
		});

		botInstanceCache.set(tokenAlias, bot);
		console.log(`Bot instance created and cached for alias: ${tokenAlias}`);
		return bot;
	} catch (error) {
		console.error(`Error creating bot instance for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 清除指定别名的 Bot 实例缓存
 * 用于重新加载 Bot 配置时
 */
export function clearBotCache(tokenAlias: string): void {
	if (botInstanceCache.has(tokenAlias)) {
		botInstanceCache.delete(tokenAlias);
		console.log(`Bot cache cleared for alias: ${tokenAlias}`);
	}
}

/**
 * 从数据库获取所有活跃的 Bot 配置
 */
export async function getAllActiveBots() {
	try {
		// ✅ 这里查询的是元数据，不是token，所以是安全的
		return await db.query.bots.findMany({
			where: eq(bots.status, 'active')
		});
	} catch (error) {
		console.error('Error fetching active bots:', error);
		return [];
	}
}
/**
 * 获取当前缓存的 Bot 实例数量
 */
export function getCachedBotCount(): number {
	return botInstanceCache.size;
}

/**
 * 获取所有缓存的 Bot 别名列表
 */
export function getCachedBotAliases(): string[] {
	return Array.from(botInstanceCache.keys());
}

/**
 * 验证 Bot Token 格式
 */
export function validateBotToken(token: string): boolean {
	// Telegram Bot Token 格式: 数字:字母数字字符串
	const tokenRegex = /^\d+:[A-Za-z0-9_-]+$/;
	return tokenRegex.test(token);
}

/**
 * 预热 Bot 实例缓存
 * 在应用启动时调用，预先创建常用的 Bot 实例
 */
export async function warmupBotCache(): Promise<void> {
	try {
		const activeBots = await getAllActiveBots();

		for (const botConfig of activeBots) {
			await getBotInstance(botConfig.tokenAlias);
		}

		console.log(`Bot cache warmed up with ${activeBots.length} instances`);
	} catch (error) {
		console.error('Error warming up bot cache:', error);
	}
}
