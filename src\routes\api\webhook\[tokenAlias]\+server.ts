// src/routes/api/webhook/[tokenAlias]/+server.ts
// CipherX TMA - Telegram Bot Webhook 入口

import { json, error } from '@sveltejs/kit';
import { webhookCallback } from 'grammy';
import { getInitializedBot } from '$lib/server/bot';
import type { RequestHandler } from './$types';
import { devLog } from '$lib/utils';

/**
 * POST 请求处理器 - 接收 Telegram 的 Webhook 更新
 *
 * URL 格式: /api/webhook/{tokenAlias}
 * 例如: /api/webhook/usa-main-bot
 */
export const POST: RequestHandler = async ({ params, request }) => {
	devLog({ log: `--- ✅ [${new Date().toISOString()}] WEBHOOK RECEIVED! ---` });
	devLog({ log: `Bot Alias: ${params.tokenAlias}` });
	const { tokenAlias } = params;

	try {
		// 验证 tokenAlias 参数
		if (!tokenAlias || typeof tokenAlias !== 'string') {
			console.error('Invalid tokenAlias:', tokenAlias);
			throw error(400, 'Invalid token alias');
		}

		// 获取已初始化的 Bot 实例
		const bot = await getInitializedBot(tokenAlias);
		if (!bot) {
			console.error(`Bot instance not found for alias: ${tokenAlias}`);
			throw error(404, 'Bot not found');
		}

		// 创建 grammY 的 webhook callback
		const handleUpdate = webhookCallback(bot, 'std/http');

		return handleUpdate(request);
	} catch (err) {
		// 错误处理和日志记录
		console.error(`Webhook error for bot ${tokenAlias}:`, err);

		// 如果是已知的 SvelteKit 错误，直接抛出
		if (err && typeof err === 'object' && 'status' in err) {
			throw err;
		}

		// 其他错误返回 500
		throw error(500, 'Internal server error');
	}
};

/**
 * GET 请求处理器 - 用于健康检查和调试
 * 返回 Bot 的基本状态信息
 */
export const GET: RequestHandler = async ({ params }) => {
	const { tokenAlias } = params;

	try {
		// 验证 tokenAlias 参数
		if (!tokenAlias || typeof tokenAlias !== 'string') {
			throw error(400, 'Invalid token alias');
		}

		// 获取已初始化的 Bot 实例
		const bot = await getInitializedBot(tokenAlias);
		if (!bot) {
			throw error(404, 'Bot not found');
		}

		// 获取 Bot 信息
		const botInfo = await bot.api.getMe();

		return json({
			success: true,
			bot: {
				id: botInfo.id,
				username: botInfo.username,
				first_name: botInfo.first_name,
				is_bot: botInfo.is_bot
			},
			webhook: {
				alias: tokenAlias,
				endpoint: `/api/webhook/${tokenAlias}`
			},
			timestamp: new Date().toISOString()
		});
	} catch (err) {
		console.error(`GET webhook error for bot ${tokenAlias}:`, err);

		// 如果是已知的 SvelteKit 错误，直接抛出
		if (err && typeof err === 'object' && 'status' in err) {
			throw err;
		}

		// 其他错误返回 500
		throw error(500, 'Internal server error');
	}
};

/**
 * OPTIONS 请求处理器 - 支持 CORS 预检请求
 */
export const OPTIONS: RequestHandler = async () => {
	return new Response(null, {
		status: 200,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		}
	});
};
