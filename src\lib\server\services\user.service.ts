import { db } from '$lib/server/db';
import { users, keys } from '$lib/server/db/schema';
import {
	displayUserProfileSchema,
	onboardingFormSchema,
	profileForScoreSchema,
	updateUserProfileSchema,
	type DisplayUserProfile,
	type OnboardingFormData,
	type ProfileForScore,
	type UpdateUserProfileData
} from '$lib/schemas/profile.schema';
import type { TelegramUserFromSDK } from '$lib/schemas/auth.schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { calculateProfileCompleteness, getZodKeys } from '$utils/user/user.utils';
import { ID_PREFIX_INVITE_CODE, ID_PREFIX_USER } from '$lib/constants/prefixes';
import { devLog } from '$utils';

/**
 * 获取用于公开展示的、安全的个人资料。
 * @param userId - 要查询的用户ID。
 * @returns 返回符合 DisplayUserProfile schema 的对象，或 undefined。
 */
export async function getDisplayUserProfile(
	userId: string
): Promise<DisplayUserProfile | undefined> {
	const user = await db.query.users.findFirst({
		where: eq(users.id, userId),
		// ✅ 使用工具函数，从 Zod schema 自动生成要查询的列，确保不多不少
		columns: getZodKeys(displayUserProfileSchema)
	});
	if (!user) return undefined;
	// ✅ 使用 .parse 来确保返回的数据 100% 符合我们的“视图”定义
	return displayUserProfileSchema.parse(user);
}

/**
 * 获取用于填充 Onboarding 表单的初始数据。
 * @param userId - 要查询的用户ID。
 * @returns 返回符合 OnboardingFormData schema 的对象，或 undefined。
 */
export async function getOnboardingData(userId: string): Promise<OnboardingFormData | undefined> {
	const user = await db.query.users.findFirst({
		where: eq(users.id, userId),
		columns: getZodKeys(onboardingFormSchema)
	});
	if (!user) return undefined;
	return onboardingFormSchema.parse(user);
}
/**
 * 根据Telegram用户数据查找或创建用户。
 * 整个过程是事务性的，并总是返回一个符合 DisplayUserProfile 形状的、
 * 可供客户端安全使用的用户对象。
 * @param telegramUser - 经过验证的、来自SDK的用户对象。
 * @param startParam - (可选) 启动参数，可能包含邀请者ID。
 * @returns 返回一个 Promise，其解析值为 DisplayUserProfile 对象。
 */
export async function findOrCreateUser(
	telegramUser: TelegramUserFromSDK,
	startParam?: string
): Promise<DisplayUserProfile> {
	// ✅ 1. 将所有数据库操作包裹在一个事务中，确保原子性
	const userFromDb = await db.transaction(async (tx) => {
		const existingUser = await tx.query.users.findFirst({
			where: eq(users.telegramUserId, telegramUser.id)
		});

		// --- 路径A：用户已存在 ---
		if (existingUser) {
			devLog({ log: `[UserService] Found existing user: ${existingUser.id}` });

			// 检查并修复可能丢失的 Lucia key，增强系统健壮性
			const existingKey = await tx.query.keys.findFirst({
				where: eq(keys.userId, existingUser.id)
			});
			if (!existingKey) {
				devLog({ log: `[UserService] Healing user ${existingUser.id}: creating missing key.` });
				await tx.insert(keys).values({
					id: `telegram:${telegramUser.id}`,
					userId: existingUser.id,
					hashedPassword: null
				});
			}

			// 更新用户的易变信息，并使用 .returning() 高效获取最新数据
			const [updatedUser] = await tx
				.update(users)
				.set({
					telegramUsername: telegramUser.username,
					profileImageUrl: telegramUser.photoUrl,
					lastActiveAt: new Date()
				})
				.where(eq(users.id, existingUser.id))
				.returning();

			return updatedUser;
		}
		// --- 路径B：用户不存在，需要创建 ---
		else {
			devLog({ log: `[UserService] Creating new user for Telegram ID: ${telegramUser.id}` });
			const userId: string = ID_PREFIX_USER + nanoid(15);
			const kinkMapCode = nanoid(10).toUpperCase();

			const inviterId = startParam?.startsWith(ID_PREFIX_INVITE_CODE)
				? startParam.replace(ID_PREFIX_INVITE_CODE, '')
				: null;

			// 为计算初始分数准备一个最小化的 profile 对象
			const profileForScore = {
				nickname: telegramUser.firstName || `User ${telegramUser.id.toString().slice(-4)}`,
				languageCode: telegramUser.languageCode
			};
			const initialScore = calculateProfileCompleteness(profileForScore);

			// 插入新用户记录，并使用 .returning() 获取创建后的完整对象
			const [createdUser] = await tx
				.insert(users)
				.values({
					id: userId,
					telegramUserId: telegramUser.id,
					kinkMapCode: kinkMapCode,
					nickname: profileForScore.nickname,
					telegramUsername: telegramUser.username,
					languageCode: profileForScore.languageCode,
					profileImageUrl: telegramUser.photoUrl,
					inviterId: inviterId,
					profileCompletenessScore: initialScore
					// 其他字段将使用数据库定义的默认值
				})
				.returning();

			// 为新用户创建关联的 Lucia key
			await tx.insert(keys).values({
				id: `telegram:${telegramUser.id}`,
				userId: createdUser.id,
				hashedPassword: null
			});

			return createdUser;
		}
	});

	if (!userFromDb) {
		// 如果事务成功但没有返回用户（理论上不应发生），抛出错误
		throw new Error('Failed to find or create user within the transaction.');
	}

	// ✅ 2. 在函数的最后，使用 Zod schema 来“解析和筛选”数据。
	//    这确保了无论内部返回了多少字段，最终暴露给上层的，
	//    永远是符合我们 DisplayUserProfile 契约的、干净安全的对象。
	return displayUserProfileSchema.parse(userFromDb);
}

export async function updateUserProfile(
	userId: string,
	data: UpdateUserProfileData
): Promise<DisplayUserProfile> {
	// 1. 使用精确的 schema 验证入参
	const validatedUpdateData = updateUserProfileSchema.parse(data);

	return await db.transaction(async (tx) => {
		try {
			// 2. 读取当前数据用于计算新分数
			const currentUserData = await tx.query.users.findFirst({
				where: eq(users.id, userId),
				columns: getZodKeys(profileForScoreSchema) // 精确查询
			});
			if (!currentUserData) throw new Error('error_user_not_found');

			// 3. 计算分数
			const profileForScore: ProfileForScore = { ...currentUserData, ...validatedUpdateData };
			const newScore = calculateProfileCompleteness(profileForScore);

			// ✅ 4. 关键优化：创建一个“干净”的更新对象
			//    我们只保留 validatedUpdateData 中值不是 undefined 的字段
			const cleanUpdateData: Partial<typeof users.$inferInsert> = {};
			for (const key in validatedUpdateData) {
				const K = key as keyof UpdateUserProfileData;
				if (validatedUpdateData[K] !== undefined) {
					(cleanUpdateData as any)[K] = validatedUpdateData[K];
				}
			}

			// 5. 使用这个干净的对象进行更新
			const [updatedUser] = await tx
				.update(users)
				.set({
					...cleanUpdateData, // ✅ 现在这里非常安全
					profileCompletenessScore: newScore,
					updatedAt: new Date()
				})
				.where(eq(users.id, userId))
				.returning();

			if (!updatedUser) throw new Error('Update failed to return user profile.');

			// 6. 使用 schema 解析，确保返回值的契约
			return displayUserProfileSchema.parse(updatedUser);
		} catch (error) {
			console.error(`[UserService] Failed to update profile for user ${userId}:`, error);
			throw new Error('error_database_update_failed');
		}
	});
}
