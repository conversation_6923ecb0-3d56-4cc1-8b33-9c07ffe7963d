{"name": "cipherX", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "cross-env TZ=UTC vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:generate": "drizzle-kit generate:pg", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "script:run": "tsx"}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/js": "^9.31.0", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.525.0", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/kit": "^2.24.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/node": "^22.16.4", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.11.0", "formsnap": "^2.0.1", "globals": "^16.3.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.36.10", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "sveltekit-rate-limiter": "^0.7.0", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vite": "7.0.1", "vite-plugin-devtools-json": "^0.3.0"}, "dependencies": {"@google/gemini-cli": "^0.1.12", "@inlang/paraglide-js": "^2.2.0", "@lottiefiles/dotlottie-web": "^0.47.0", "@lucia-auth/adapter-drizzle": "^1.1.0", "@node-rs/argon2": "2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@sveltejs/enhanced-img": "^0.7.0", "@telegram-apps/init-data-node": "^2.0.10", "@telegram-apps/sdk-svelte": "^2.0.30", "@tonconnect/ui": "^2.2.0", "@vitejs/plugin-basic-ssl": "^2.1.0", "country-state-city": "^3.2.1", "croner": "^9.1.0", "devalue": "^5.1.1", "dotenv": "^17.2.0", "grammy": "^1.37.0", "konsta": "^4.0.1", "lucia": "^3.2.2", "nanoid": "^5.1.5", "pg": "^8.16.3", "postgres": "^3.4.7", "svelecte": "^5.2.0", "sveltekit-superforms": "2.27.1", "vite-plugin-mkcert": "^1.17.8", "zod": "^3.23.8"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}