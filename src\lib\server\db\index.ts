// src/lib/server/db/index.ts
// CipherX TMA - 数据库连接和配置 (使用 postgres.js)

import { drizzle } from 'drizzle-orm/postgres-js'; // 👈 修正点 1: 导入 postgres.js 对应的适配器
import postgres from 'postgres';                 // 👈 修正点 2: 从 'postgres' 导入主函数
import { env } from '$env/dynamic/private';
import * as schema from './schema'; // 导入我们聚合后的 schema 入口

// 1. 检查环境变量
if (!env.DATABASE_URL) {
	throw new Error('FATAL: DATABASE_URL environment variable is not set');
}

// 2. 创建 postgres.js 的客户端实例
// postgres.js 在内部管理连接池，所以我们只需要创建一个实例
const client = postgres(env.DATABASE_URL);

// 3. 创建 Drizzle 数据库实例，并传入 postgres.js 的客户端和我们完整的 schema
export const db = drizzle(client, {
    schema,
    logger: process.env.NODE_ENV === 'development' // 开发环境启用日志
});

/**
 * 数据库健康检查函数
 * 用于 /healthcheck 端点或部署平台的健康检查
 */
export async function checkDatabaseHealth(): Promise<{ ok: boolean; error?: string }> {
    try {
			// postgres.js 客户端直接提供了执行查询的方法
			// 它会自动处理连接的获取和释放
			await client`SELECT 1`;
			return { ok: true };
		} catch (error) {
			console.error('Database health check failed:', error);
			return { ok: false, error: error instanceof Error ? error.message : 'Unknown error' };
		}
}

/**
 * (在应用优雅退出时调用) 关闭所有数据库连接
 */
export async function closeDatabaseConnection(): Promise<void> {
	try {
		// 调用 postgres.js 客户端的 end() 方法来关闭连接池
		await client.end();
		console.log('Database connection pool closed.');
	} catch (error) {
		console.error('Error closing database connection pool:', error);
	}
}