// src/lib/schemas/group.schema.ts
// CipherX TMA - 群组相关验证规则 (V2.1 - 国际化重构版)

import { z } from 'zod';
import {
	createIdSchema,
	countryCodeSchema,
	telegramUserIdSchema
} from '$lib/schemas/common.schema.js';
import { GROUP_THEMES } from '$lib/constants/community';
import { groupRoleEnum, groupStatusEnum } from '$lib/server/db/schema/_enums';
import { ID_PREFIX_USER, ID_PREFIX_GROUP, ID_PREFIX_BOT } from '$lib/constants/prefixes.js';

/**
 * 单个语言翻译的验证 Schema
 */
export const groupTranslationSchema = z.object({
	languageCode: z.string().min(2), // e.g., 'en', 'zh-Hans', 'ja'
	name: z.string().min(2, 'lib_validation_group_name_too_short').max(100),
	description: z.string().min(10, 'lib_validation_group_desc_too_short').max(500).optional()
});

/**
 * 创建群组时的验证 Schema (V2.1)
 * 这是由我们的Bot内部调用时，需要满足的数据结构
 */
export const createGroupSchema = z.object({
	slug: z
		.string()
		.min(3, 'lib_validation_slug_too_short')
		.max(100, 'lib_validation_slug_too_long')
		.regex(/^[a-z0-9-]+$/, 'lib_validation_slug_format'),

	telegramChatId: z.bigint(), // ✅ 修正为 bigint，与数据库保持一致

	countryCode: countryCodeSchema,
	theme: z.enum(Object.values(GROUP_THEMES) as [string, ...string[]]),
	creatorId: createIdSchema(ID_PREFIX_USER),
	botId: createIdSchema(ID_PREFIX_BOT),

	// 翻译内容必须至少提供一个
	translations: z.array(groupTranslationSchema).min(1, 'lib_validation_translation_required')
});

/**
 * 更新群组翻译内容时，用于后台表单的 Schema
 */
export const updateGroupTranslationsSchema = z.object({
	groupId: createIdSchema(ID_PREFIX_GROUP),
	translations: z.array(groupTranslationSchema).min(1)
});

/**
 * 更新群组状态的验证 Schema
 */
export const updateGroupStatusSchema = z.object({
	groupId: createIdSchema(ID_PREFIX_GROUP),
	status: z.enum(groupStatusEnum.enumValues)
});

/**
 * 加入群组操作的验证 Schema (保持不变)
 */
export const joinGroupSchema = z.object({
	groupId: createIdSchema(ID_PREFIX_GROUP)
});

/**
 * 群组搜索过滤器验证 Schema (保持不变)
 */
export const groupSearchFiltersSchema = z.object({
	countryCode: countryCodeSchema.optional(),
	theme: z.enum(Object.values(GROUP_THEMES) as [string, ...string[]]).optional()
	// ... 其他筛选条件
});

/**
 * 更新群组成员角色的验证 Schema (保持不变)
 */
export const updateGroupRoleSchema = z.object({
	userId: createIdSchema(ID_PREFIX_USER),
	groupId: createIdSchema(ID_PREFIX_GROUP),
	newRole: z.enum(groupRoleEnum.enumValues)
});

// --- 导出Zod推断的TypeScript类型 ---
export type CreateGroupData = z.infer<typeof createGroupSchema>;
export type UpdateGroupTranslationsData = z.infer<typeof updateGroupTranslationsSchema>;
export type UpdateGroupStatusData = z.infer<typeof updateGroupStatusSchema>;
export type JoinGroupData = z.infer<typeof joinGroupSchema>;
export type GroupSearchFilters = z.infer<typeof groupSearchFiltersSchema>;
export type UpdateGroupRoleData = z.infer<typeof updateGroupRoleSchema>;
export type GroupTranslationData = z.infer<typeof groupTranslationSchema>;
