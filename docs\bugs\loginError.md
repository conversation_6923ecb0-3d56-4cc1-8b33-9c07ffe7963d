Query: select "users"."id", "users"."telegram_user_id", "users"."ton_wallet_address", "users"."inviter_id", "users"."kink_map_code", "users"."nickname", "users"."telegram_username", "users"."age", "users"."height_cm", "users"."weight_kg", "users"."country_code", "users"."province_code", "users"."city", "users"."language_code", "users"."bio", "users"."profile_image_url", "users"."orientation", "users"."body_type", "users"."presentation_style", "users"."relationship_status", "users"."kink_category_bitmask", "users"."kink_ratings", "users"."profile_completeness_score", "users"."trust_score", "users"."vip_level", "users"."point_balance", "users"."is_active", "users"."is_banned", "users"."created_at", "users"."updated_at", "users"."last_active_at", "sessions"."id", "sessions"."user_id", "sessions"."expires_at" from "sessions" inner join "users" on "sessions"."user_id" = "users"."id" where "sessions"."id" = $1 -- params: ["jzzcpqc2jfjw2tdrofjcpnuwdkcih5iaxarpvogq"]
Query: select "id", "telegram_user_id", "kink_map_code", "nickname", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "last_active_at", "profile_completeness_score", "kink_ratings", "kink_category_bitmask", "vip_level", "is_banned", "trust_score" from "users" "users" where "users"."id" = $1 limit $2 -- params: ["usr_74yzcsqLXerkGFY", 1]
Query: select "users"."id", "users"."telegram_user_id", "users"."ton_wallet_address", "users"."inviter_id", "users"."kink_map_code", "users"."nickname", "users"."telegram_username", "users"."age", "users"."height_cm", "users"."weight_kg", "users"."country_code", "users"."province_code", "users"."city", "users"."language_code", "users"."bio", "users"."profile_image_url", "users"."orientation", "users"."body_type", "users"."presentation_style", "users"."relationship_status", "users"."kink_category_bitmask", "users"."kink_ratings", "users"."profile_completeness_score", "users"."trust_score", "users"."vip_level", "users"."point_balance", "users"."is_active", "users"."is_banned", "users"."created_at", "users"."updated_at", "users"."last_active_at", "sessions"."id", "sessions"."user_id", "sessions"."expires_at" from "sessions" inner join "users" on "sessions"."user_id" = "users"."id" where "sessions"."id" = $1 -- params: ["jzzcpqc2jfjw2tdrofjcpnuwdkcih5iaxarpvogq"]
Query: select "id", "telegram_user_id", "kink_map_code", "nickname", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "last_active_at", "profile_completeness_score", "kink_ratings", "kink_category_bitmask", "vip_level", "is_banned", "trust_score" from "users" "users" where "users"."id" = $1 limit $2 -- params: ["usr_74yzcsqLXerkGFY", 1]
Query: select "users"."id", "users"."telegram_user_id", "users"."ton_wallet_address", "users"."inviter_id", "users"."kink_map_code", "users"."nickname", "users"."telegram_username", "users"."age", "users"."height_cm", "users"."weight_kg", "users"."country_code", "users"."province_code", "users"."city", "users"."language_code", "users"."bio", "users"."profile_image_url", "users"."orientation", "users"."body_type", "users"."presentation_style", "users"."relationship_status", "users"."kink_category_bitmask", "users"."kink_ratings", "users"."profile_completeness_score", "users"."trust_score", "users"."vip_level", "users"."point_balance", "users"."is_active", "users"."is_banned", "users"."created_at", "users"."updated_at", "users"."last_active_at", "sessions"."id", "sessions"."user_id", "sessions"."expires_at" from "sessions" inner join "users" on "sessions"."user_id" = "users"."id" where "sessions"."id" = $1 -- params: ["jzzcpqc2jfjw2tdrofjcpnuwdkcih5iaxarpvogq"]
Query: select "id", "telegram_user_id", "kink_map_code", "nickname", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "last_active_at", "profile_completeness_score", "kink_ratings", "kink_category_bitmask", "vip_level", "is_banned", "trust_score" from "users" "users" where "users"."id" = $1 limit $2 -- params: ["usr_74yzcsqLXerkGFY", 1]
An unexpected error occurred: {
  error: SchemaError: No shape could be created for schema.
      at schemaShape (D:\dev-storage\pnpm\store\v10\links\sveltekit-superforms\2.27.1\af5cfcbd3e8ba5ee993a149b2a936d56f1a832e807ac5e2527930c26911c0ac0\node_modules\sveltekit-superforms\dist\jsonSchema\schemaShape.js:7:15)
      at createAdapter (D:\dev-storage\pnpm\store\v10\links\sveltekit-superforms\2.27.1\af5cfcbd3e8ba5ee993a149b2a936d56f1a832e807ac5e2527930c26911c0ac0\node_modules\sveltekit-superforms\dist\adapters\adapters.js:38:27)
      at _zod (D:\dev-storage\pnpm\store\v10\links\sveltekit-superforms\2.27.1\af5cfcbd3e8ba5ee993a149b2a936d56f1a832e807ac5e2527930c26911c0ac0\node_modules\sveltekit-superforms\dist\adapters\zod.js:29:25)   
      at memoized (D:\dev-storage\pnpm\store\v10\links\memoize-weak\1.0.2\7acb6e4d6d8064bfa0e8e0b4a73781a76805153a894794fd9831da73d6a4b3bd\node_modules\memoize-weak\lib\memoize.js:79:20)
      at load (D:\dev\TMA\blueX\src\routes\(public)\onboarding\+page.ts:41:48)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async load_data (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\load_data.js:204:17)
      at async eval (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\index.js:183:13) {
    path: ''
  },
  user: 'usr_74yzcsqLXerkGFY',
  route: '/(public)/onboarding'
}
Query: select "id", "telegram_user_id", "kink_map_code", "nickname", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "last_active_at", "profile_completeness_score", "kink_ratings", "kink_category_bitmask", "vip_level", "is_banned", "trust_score" from "users" "users" where "users"."id" = $1 limit $2 -- params: ["usr_74yzcsqLXerkGFY", 1]
An unexpected error occurred: {
  error: Error: Data returned from `load` while rendering /(public)/onboarding is not serializable: Cannot stringify arbitrary non-POJOs (data.user)
      at get_data (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\render.js:663:9)
      at render_response (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\render.js:288:27)
      at async render_page (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\index.js:252:15)
      at async resolve (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\respond.js:501:18)
      at async paraglideMiddleware (D:\dev\TMA\blueX\src\lib\paraglide\server.js:113:22)
      at async respond (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\respond.js:365:20)
      at async file:///D:/dev-storage/pnpm/store/v10/links/@sveltejs/kit/2.24.0/6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d/node_modules/@sveltejs/kit/src/exports/vite/dev/index.js:544:22,
  user: 'usr_74yzcsqLXerkGFY',
  route: '/(public)/onboarding'
}
An unexpected error occurred: {
  error: Error: Data returned from `load` while rendering /(public)/onboarding is not serializable: Cannot stringify arbitrary non-POJOs (data.user)
      at get_data (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\render.js:663:9)
      at render_response (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\render.js:288:27)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async respond_with_error (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\respond_with_error.js:86:10)
      at async render_page (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\page\index.js:324:10)
      at async resolve (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\respond.js:501:18)
      at async paraglideMiddleware (D:\dev\TMA\blueX\src\lib\paraglide\server.js:113:22)
      at async respond (D:\dev-storage\pnpm\store\v10\links\@sveltejs\kit\2.24.0\6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d\node_modules\@sveltejs\kit\src\runtime\server\respond.js:365:20)
      at async file:///D:/dev-storage/pnpm/store/v10/links/@sveltejs/kit/2.24.0/6251b3710309455496e9c4400e561a3191acba2e32481e3566b545d1eb064b5d/node_modules/@sveltejs/kit/src/exports/vite/dev/index.js:544:22,
  user: 'usr_74yzcsqLXerkGFY',
  route: '/(public)/onboarding'
}
02:58:28 [vite] (client) ✨ new dependencies optimized: devalue, zod, @lottiefiles/dotlottie-web, country-state-city
02:58:28 [vite] (client) ✨ optimized dependencies changed. reloading