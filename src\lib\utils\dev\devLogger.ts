// src/lib/utils/devLogger.ts
import { dev } from '$app/environment';

function getCallerInfo(): string {
	if (!dev) return '';
	try {
		const err = new Error();
		const stack = err.stack || '';
		const regex = /\((.*):(\d+):(\d+)\)|at\s+(.*):(\d+):(\d+)/g;
		let match;
		let callerInfo = '';

		while ((match = regex.exec(stack)) !== null) {
			const filePath = match[1] || match[4];
			const lineNumber = match[2] || match[5];
			if (filePath && !filePath.includes('devLogger.ts')) {
				const parts = filePath.split('/');
				const fileName = parts[parts.length - 1];
				callerInfo = `${fileName}:${lineNumber}`;
				break;
			}
		}
		return callerInfo || 'unknown:unknown';
	} catch (e) {
		return 'errorGettingCallerInfo:unknown';
	}
}

/**
 * 在开发环境下以特定格式打印日志，自动获取变量名
 * @param obj 包含要打印变量的对象，例如：{ myVariable }
 */
export function devLog(obj: Record<string, any>): void {
	if (dev) {
		const callerInfo = getCallerInfo();
		// 遍历传入对象的所有键值对
		for (const key in obj) {
			if (Object.prototype.hasOwnProperty.call(obj, key)) {
				console.log(`=========\n${callerInfo} : ${key} :`, obj[key], `\n===`);
			}
		}
	}
}

/**
 * 带有警告类型的开发环境日志，自动获取变量名
 * @param obj 包含要打印变量的对象
 */
export function devWarn(obj: Record<string, any>): void {
	if (dev) {
		const callerInfo = getCallerInfo();
		for (const key in obj) {
			if (Object.prototype.hasOwnProperty.call(obj, key)) {
				console.warn(`[DEV WARN] =========\n${callerInfo} : ${key} :`, obj[key], `\n===`);
			}
		}
	}
}

/**
 * 带有错误类型的开发环境日志，自动获取变量名
 * @param obj 包含要打印变量的对象
 */
export function devError(obj: Record<string, any>): void {
	if (dev) {
		const callerInfo = getCallerInfo();
		for (const key in obj) {
			if (Object.prototype.hasOwnProperty.call(obj, key)) {
				console.error(`[DEV ERROR] =========\n${callerInfo} : ${key} :`, obj[key], `\n===`);
			}
		}
	}
}
