No overload matches this call.
  Overload 1 of 2, '(adapter: ValidationAdapter<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }, { ...; }>, options?: Partial<...> | undefined): Promise<...>', gave the following error.
    Argument of type '{ nickname: string | null; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; countryCode: string | null; provinceCode: string | null; ... 15 more ...; lastActiveAt: Date; } | undefined' is not assignable to parameter of type 'ValidationAdapter<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }, { ...; }>'.
      Type 'undefined' is not assignable to type 'ValidationAdapter<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }, { ...; }>'.
        Type 'undefined' is not assignable to type 'ClientValidationAdapter<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }, { ...; }>'.
  Overload 2 of 2, '(data: SuperValidateData<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }>, adapter: ValidationAdapter<...>, options?: Partial<...> | undefined): Promise<...>', gave the following error.
    Argument of type '{ nickname: string | null; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; countryCode: string | null; provinceCode: string | null; ... 15 more ...; lastActiveAt: Date; } | undefined' is not assignable to parameter of type 'SuperValidateData<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }>'.
      Type '{ nickname: string | null; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; countryCode: string | null; provinceCode: string | null; ... 15 more ...; lastActiveAt: Date; }' is not assignable to type 'SuperValidateData<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }>'.
        Type '{ nickname: string | null; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; countryCode: string | null; provinceCode: string | null; ... 15 more ...; lastActiveAt: Date; }' is not assignable to type 'Partial<{ nickname: string; age: number | null; heightCm: number | null; weightKg: number | null; profileImageUrl: string | null; languageCode: string | null; countryCode: string | null; ... 8 more ...; kinkRatings: Record<...> | null; }>'.
          Types of property 'nickname' are incompatible.
            Type 'string | null' is not assignable to type 'string | undefined'.
              Type 'null' is not assignable to type 'string | undefined'.ts(2769)
⚠ Error (TS2769)  | 

No overload matches this call.
   

Overload 1 of 2:
gave the following error.
          	Argument of type:
is not assignable to parameter of type:
.
                	Type 
 is not assignable to type:
.
                      	Type 
 is not assignable to type:
.
   

Overload 2 of 2:
gave the following error.
          	Argument of type:
is not assignable to parameter of type:
.
                	Type:
is not assignable to type:
.
                      	Type:
is not assignable to type:
.
                            	Types of property nickname are incompatible.
                                  	Type 
 is not assignable to type 
 .
                                        	Type 
 is not assignable to type 
 .
let finalUserData: {
    nickname: string | null;
    age: number | null;
    heightCm: number | null;
    weightKg: number | null;
    profileImageUrl: string | null;
    countryCode: string | null;
    provinceCode: string | null;
    city: string | null;
    bio: string | null;
    orientation: "straight" | "gay" | "lesbian" | "bisexual" | "asexual" | "demisexual" | "pansexual" | "queer" | "fluid" | "other" | "prefer_not_to_say" | null;
    bodyType: "prefer_not_to_say" | "male_body" | "female_body" | "other_body_type" | null;
    presentationStyle: "other" | "prefer_not_to_say" | "conventional_masculine" | "rugged_masculine" | "feminine" | "androgynous_neutral" | null;
    ... 10 more ...;
    lastActiveAt: Date;
} | undefined


------ 


Argument of type 'ZodObject<{ age: ZodNullable<ZodCoercedNumber<unknown>>; heightCm: ZodNullable<ZodCoercedNumber<unknown>>; weightKg: ZodNullable<ZodCoercedNumber<unknown>>; ... 12 more ...; nickname: ZodString; }, $strip>' is not assignable to parameter of type 'ZodObjectType'.
  Type 'ZodObject<{ age: ZodNullable<ZodCoercedNumber<unknown>>; heightCm: ZodNullable<ZodCoercedNumber<unknown>>; weightKg: ZodNullable<ZodCoercedNumber<unknown>>; ... 12 more ...; nickname: ZodString; }, $strip>' is missing the following properties from type 'ZodType<Record<string, unknown>, ZodTypeDef, Record<string, unknown> | undefined>': _type, _parse, _getType, _getOrReturnCtx, and 7 more.ts(2345)
⚠ Error (TS2345)  | 

Argument of type:
is not assignable to parameter of type 
 .
   

Type:
is missing the following properties from type :
:
_type
_parse
_getType
_getOrReturnCtx
and 7 more.
(alias) const onboardingFormSchema: ZodObject<{
    age: ZodNullable<ZodCoercedNumber<unknown>>;
    heightCm: ZodNullable<ZodCoercedNumber<unknown>>;
    weightKg: ZodNullable<ZodCoercedNumber<unknown>>;
    ... 12 more ...;
    nickname: ZodString;
}, $strip>
import onboardingFormSchema 