#!/usr/bin/env tsx
// scripts/setup-webhook.ts
// CipherX TMA - Webhook Setup Script

import { Bot } from 'grammy';
import 'dotenv/config';
import { fileURLToPath } from 'node:url';

interface WebhookConfig {
	tokenAlias: string;
	botToken: string;
	webhookUrl: string;
}

/**
 * Get bot token from environment variables
 */
function getBotToken(tokenAlias: string): string | null {
	// Try different environment variable formats
	const envKeys = [
		`BOT_TOKEN_${tokenAlias.toUpperCase().replace(/-/g, '_')}`,
		`BOT_TOKEN_${tokenAlias.toUpperCase()}`,
		'BOT_TOKEN' // fallback
	];

	for (const key of envKeys) {
		const token = process.env[key];
		if (token) {
			return token;
		}
	}

	return null;
}

/**
 * Set webhook for a specific bot
 */
async function setWebhook(config: WebhookConfig): Promise<boolean> {
	try {
		console.log(`🔧 Setting webhook for ${config.tokenAlias}...`);

		const bot = new Bot(config.botToken);

		// Set the webhook
		await bot.api.setWebhook(config.webhookUrl);

		// Verify webhook was set
		const webhookInfo = await bot.api.getWebhookInfo();

		console.log(`✅ Webhook set successfully for ${config.tokenAlias}`);
		console.log(`   URL: ${webhookInfo.url}`);
		console.log(`   Pending updates: ${webhookInfo.pending_update_count}`);

		return true;
	} catch (error) {
		console.error(`❌ Failed to set webhook for ${config.tokenAlias}:`, error);
		return false;
	}
}

/**
 * Remove webhook for a specific bot
 */
async function removeWebhook(tokenAlias: string, botToken: string): Promise<boolean> {
	try {
		console.log(`🗑️ Removing webhook for ${tokenAlias}...`);

		const bot = new Bot(botToken);
		await bot.api.deleteWebhook();

		console.log(`✅ Webhook removed for ${tokenAlias}`);
		return true;
	} catch (error) {
		console.error(`❌ Failed to remove webhook for ${tokenAlias}:`, error);
		return false;
	}
}

/**
 * Get webhook info for a specific bot
 */
async function getWebhookInfo(tokenAlias: string, botToken: string): Promise<void> {
	try {
		console.log(`ℹ️ Getting webhook info for ${tokenAlias}...`);

		const bot = new Bot(botToken);
		const webhookInfo = await bot.api.getWebhookInfo();

		console.log(`📊 Webhook Info for ${tokenAlias}:`);
		console.log(`   URL: ${webhookInfo.url || 'Not set'}`);
		console.log(`   Has custom certificate: ${webhookInfo.has_custom_certificate}`);
		console.log(`   Pending updates: ${webhookInfo.pending_update_count}`);
		console.log(`   Last error date: ${webhookInfo.last_error_date || 'None'}`);
		console.log(`   Last error message: ${webhookInfo.last_error_message || 'None'}`);
		console.log(`   Max connections: ${webhookInfo.max_connections}`);
		console.log(`   Allowed updates: ${webhookInfo.allowed_updates?.join(', ') || 'All'}`);
	} catch (error) {
		console.error(`❌ Failed to get webhook info for ${tokenAlias}:`, error);
	}
}

/**
 * Main function
 */
async function main() {
	const args = process.argv.slice(2);
	const command = args[0];
	const tokenAlias = args[1];

	if (!command) {
		console.log(`
🤖 CipherX TMA Webhook Setup Script

Usage:
  tsx scripts/setup-webhook.ts <command> [options]

Commands:
  set <token-alias>     Set webhook for a specific bot
  remove <token-alias>  Remove webhook for a specific bot
  info <token-alias>    Get webhook info for a specific bot
  list                  List all configured bots
  help                  Show this help message

Examples:
  tsx scripts/setup-webhook.ts set usa-main-bot
  tsx scripts/setup-webhook.ts remove dev-bot
  tsx scripts/setup-webhook.ts info china-community-bot
  tsx scripts/setup-webhook.ts list

Environment Variables Required:
  WEBHOOK_BASE_URL - Base URL for webhooks
  BOT_TOKEN_<ALIAS> - Bot token for each alias
		`);
		return;
	}

	const baseUrl = process.env.WEBHOOK_BASE_URL;
	if (!baseUrl && ['set'].includes(command)) {
		console.error('❌ WEBHOOK_BASE_URL environment variable is required');
		return;
	}

	switch (command) {
		case 'set':
			if (!tokenAlias) {
				console.error('❌ Token alias is required for set command');
				return;
			}

			const botToken = getBotToken(tokenAlias);
			if (!botToken) {
				console.error(`❌ Bot token not found for alias: ${tokenAlias}`);
				console.error(
					`   Expected environment variable: BOT_TOKEN_${tokenAlias.toUpperCase().replace(/-/g, '_')}`
				);
				return;
			}

			const webhookUrl = `${baseUrl}/api/webhook/${tokenAlias}`;
			await setWebhook({ tokenAlias, botToken, webhookUrl });
			break;

		case 'remove':
			if (!tokenAlias) {
				console.error('❌ Token alias is required for remove command');
				return;
			}

			const removeToken = getBotToken(tokenAlias);
			if (!removeToken) {
				console.error(`❌ Bot token not found for alias: ${tokenAlias}`);
				return;
			}

			await removeWebhook(tokenAlias, removeToken);
			break;

		case 'info':
			if (!tokenAlias) {
				console.error('❌ Token alias is required for info command');
				return;
			}

			const infoToken = getBotToken(tokenAlias);
			if (!infoToken) {
				console.error(`❌ Bot token not found for alias: ${tokenAlias}`);
				return;
			}

			await getWebhookInfo(tokenAlias, infoToken);
			break;

		case 'list':
			console.log('📋 Configured Bot Tokens:');
			const envVars = Object.keys(process.env).filter((key) => key.startsWith('BOT_TOKEN_'));

			if (envVars.length === 0) {
				console.log('   No bot tokens configured');
			} else {
				envVars.forEach((envVar) => {
					const alias = envVar.replace('BOT_TOKEN_', '').toLowerCase().replace(/_/g, '-');
					console.log(`   • ${alias} (${envVar})`);
				});
			}
			break;

		case 'help':
			// Show help (same as no command)
			main();
			break;

		default:
			console.error(`❌ Unknown command: ${command}`);
			console.log('Use "tsx scripts/setup-webhook.ts help" for usage information');
	}
}

// Run the script
if (process.argv[1] === fileURLToPath(import.meta.url)) {
	main().catch(console.error);
}
