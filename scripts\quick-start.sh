#!/bin/bash
# scripts/quick-start.sh
# CipherX TMA - Quick Start Script for Bot Development

set -e

echo "🚀 CipherX TMA Bot Quick Start"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.example .env
    print_info "Please edit .env file with your actual values before continuing."
    print_info "Required variables:"
    echo "  - DATABASE_URL"
    echo "  - BOT_TOKEN_DEV_BOT (or other bot tokens)"
    echo "  - BOT_ADMIN_IDS"
    echo "  - WEBHOOK_BASE_URL (for webhook setup)"
    echo ""
    read -p "Press Enter after configuring .env file..."
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies..."
    pnpm install
    print_status "Dependencies installed"
else
    print_status "Dependencies already installed"
fi

# Check if database is accessible
print_info "Checking database connection..."
if pnpm tsx -e "
import { db } from './src/lib/server/db/index.js';
try {
    console.log('Database connection successful');
} catch (error) {
    console.error('Database connection failed:', error.message);
    process.exit(1);
}
" 2>/dev/null; then
    print_status "Database connection successful"
else
    print_error "Database connection failed"
    print_info "Please check your DATABASE_URL in .env file"
    exit 1
fi

# Start development server
print_info "Starting development server..."
echo ""
print_info "Development server will start on: http://localhost:5173"
print_info "For webhook testing, you'll need to:"
echo "  1. Install ngrok: npm install -g ngrok"
echo "  2. Run ngrok: ngrok http 5173"
echo "  3. Update WEBHOOK_BASE_URL in .env with ngrok URL"
echo "  4. Set webhook: tsx scripts/setup-webhook.ts set dev-bot"
echo ""
print_info "Starting server in 3 seconds..."
sleep 3

# Start the development server
pnpm run dev --open --host
