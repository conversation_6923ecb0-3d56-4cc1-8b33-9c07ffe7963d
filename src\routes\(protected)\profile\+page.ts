// src/routes/(protected)/profile/+page.ts
import { parse } from 'devalue';
import { get } from 'svelte/store';
import { displayUserProfileStore } from '$stores';
import type { PageLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load: PageLoad = async ({ fetch, parent }) => {
	// 同样，确保认证状态
	// await parent() 会运行父布局的 load 函数，确保 event.locals.user 和 session 被设置
	await parent();

	const userFromStore = get(displayUserProfileStore);

	if (userFromStore) {
		// 缓存命中，直接使用
		console.log('[Profile Load] ✅ Cache HIT. Using data from store.');
		return { userProfile: userFromStore };
	} else {
		// 缓存未命中，从 API 获取
		console.log('[Profile Load] ❌ Cache MISS. Fetching from /api/profile...');
		const response = await fetch('/api/profile');

		if (response.ok) {
			// ✅ 关键修正：确保 await response.text()
			// 如果你的后端 /api/profile 返回的是 devalue 序列化的字符串
			const responseText = await response.text();
			const userProfile = parse(responseText);

			// 调试日志，确认解析后的数据
			console.log('[Profile Load] ✅ Data fetched and parsed:', userProfile);

			return { userProfile };
		} else {
			// 如果响应状态码不是 2xx，抛出 SvelteKit 错误
			// 这将触发你的 hooks.server.ts 中的 handleError 或 SvelteKit 的默认错误页面
			const errorText = await response.text(); // 尝试获取错误响应体
			let errorMessage = `Could not load your profile. Status: ${response.status}`;
			try {
				// 如果错误响应是 JSON 格式，尝试解析它以获取更详细信息
				const errorJson = JSON.parse(errorText);
				errorMessage = errorJson.message || errorMessage;
			} catch (e) {
				// 如果不是 JSON，就用原始的错误文本
				errorMessage = errorText || errorMessage;
			}

			console.error('[Profile Load] ❌ Fetch error:', errorMessage);
			throw error(response.status, errorMessage);
		}
	}
};
