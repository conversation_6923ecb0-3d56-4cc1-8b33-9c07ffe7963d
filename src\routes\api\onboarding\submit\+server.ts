import { json, error } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { onboardingFormSchema } from '$lib/schemas/profile.schema';
import { updateUserProfile } from '$lib/server/services/user.service';
import type { RequestHandler } from './$types';
import { superJson } from '$lib/utils';

export const POST: RequestHandler = async ({ request, locals }) => {
	const { user } = locals;
	if (!user) {
		throw error(401, 'Unauthorized');
	}

	try {
		const data = await request.json();
		const form = await superValidate(data, zod(onboardingFormSchema));
		if (!form.valid) {
			console.error('❌ Server-side validation failed with errors:', form.errors);
			return json({ form }, { status: 400 });
		}

		// 验证通过，执行数据库更新
		const updatedProfile = await updateUserProfile(user.id, form.data);
		return superJson({ success: true, userProfile: updatedProfile });
	} catch (e) {
		// 捕获 request.json() 可能的解析错误或后续的数据库错误
		console.error('API /onboarding/submit failed:', e);
		// 如果是 Zod 错误，可以更精细地处理，但通常 superValidate 会处理
		// if (e instanceof z.ZodError) { ... }
		throw error(500, 'Failed to process your request.');
	}
};
