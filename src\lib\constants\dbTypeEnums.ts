// src/lib/constants/enums.ts

// Define the actual values here as plain arrays
// These should match what's in your $lib/server/db/schema/_enums
export const ORIENTATION_VALUES = [
	'straight',
	'gay',
	'lesbian',
	'bisexual',
	'asexual',
	'demisexual',
	'pansexual',
	'queer',
	'fluid',
	'other',
	'prefer_not_to_say'
] as const; // 'as const' makes it a readonly tuple, which is great for Zod

export const BODY_TYPE_VALUES = [
	'male_body', // Renamed from 'male' to match your en.json for consistency
	'female_body', // Renamed from 'female' to match your en.json for consistency
	'other_body_type',
	'prefer_not_to_say' // Renamed from 'other' to match your en.json for consistency
] as const;

export const PRESENTATION_STYLE_VALUES = [
	'conventional_masculine',
	'rugged_masculine', // Added from your en.json
	'feminine',
	'androgynous_neutral',
	'other',
	'prefer_not_to_say'
] as const;

export const RELATIONSHIP_STATUS_VALUES = [
	'single',
	'in_a_relationship',
	'complicated',
	'open_relationship',
	'married',
	'polyamorous',
	'other',
	'prefer_not_to_say'
] as const;

// You can also export the types directly from here if you prefer
export type OrientationType = (typeof ORIENTATION_VALUES)[number];
export type BodyType = (typeof BODY_TYPE_VALUES)[number];
export type PresentationStyle = (typeof PRESENTATION_STYLE_VALUES)[number];
export type RelationshipStatus = (typeof RELATIONSHIP_STATUS_VALUES)[number];
