// src/lib/server/bot/handlers/userOnboarding.ts
// CipherX TMA - 用户引导功能

import type { Context } from 'grammy';
import { InlineKeyboard } from 'grammy';
import { getTMAAppUrl } from './basicCommands';
import { groups } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { eq } from 'drizzle-orm';

/**
 * 根据国家代码获取本地化的欢迎消息
 */
function getLocalizedWelcomeMessage(countryCode?: string): {
	text: string;
	buttonText: string;
} {
	switch (countryCode) {
		case 'JP':
			return {
				text: '🎉 新しいメンバーがコミュニティに参加しました\\!\n\nコミュニティのルールを確認し、自己紹介をしてください\\。TMAアプリでより多くの機能をお楽しみください\\!',
				buttonText: '🚀 アプリを開く'
			};
		case 'TW':
			return {
				text: '🎉 歡迎新成員加入社群\\!\n\n請查看社群規則並介紹自己\\。開啟TMA應用程式體驗更多功能\\!',
				buttonText: '🚀 開啟應用程式'
			};
		case 'DE':
			return {
				text: '🎉 Ein neues Mitglied ist der Community beigetreten\\!\n\nBitte lest die Community\\-Regeln und stellt euch vor\\. Öffnet die TMA\\-App für mehr Funktionen\\!',
				buttonText: '🚀 App öffnen'
			};
		case 'CN':
			return {
				text: '🎉 欢迎新成员加入社群\\!\n\n请查看社群规则并介绍自己\\。打开TMA应用体验更多功能\\!',
				buttonText: '🚀 打开应用'
			};
		case 'GB':
		case 'US':
		default:
			return {
				text: '🎉 A new member has joined the community\\!\n\nPlease check the community rules and introduce yourself\\. Open the TMA app to experience more features\\!',
				buttonText: '🚀 Open App'
			};
	}
}

/**
 * 处理新成员加入事件
 */
export async function handleNewMember(ctx: Context): Promise<void> {
	try {
		// 检查是否是群组消息
		if (ctx.chat?.type !== 'supergroup') {
			return;
		}

		// 获取新加入的成员信息
		const newMembers = ctx.message?.new_chat_members;
		if (!newMembers || newMembers.length === 0) {
			return;
		}

		// 过滤掉机器人（避免欢迎其他机器人）
		const humanMembers = newMembers.filter((member) => !member.is_bot);
		if (humanMembers.length === 0) {
			return;
		}

		// 尝试从群组信息中获取国家代码
		// 这里可以通过数据库查询群组信息来获取country_code
		// 暂时使用默认的英语欢迎消息
		const countryCode = await getGroupCountryCode(ctx.chat.id);
		const { text, buttonText } = getLocalizedWelcomeMessage(countryCode);

		// 创建内联键盘
		const keyboard = new InlineKeyboard().url(buttonText, getTMAAppUrl());

		// 发送欢迎消息
		await ctx.reply(text, {
			reply_markup: keyboard,
			parse_mode: 'MarkdownV2'
		});

		console.log(
			`Welcome message sent for ${humanMembers.length} new member(s) in group ${ctx.chat.id}`
		);
	} catch (error) {
		console.error('Error handling new member:', error);
		// 发送简单的欢迎消息作为备用
		try {
			await ctx.reply('🎉 Welcome to the community!');
		} catch (fallbackError) {
			console.error('Error sending fallback welcome message:', fallbackError);
		}
	}
}

/**
 * 从数据库获取群组的国家代码
 * 这里需要实现数据库查询逻辑
 */
async function getGroupCountryCode(chatId: number): Promise<string | undefined> {
	try {
		const group = await db.select().from(groups).where(eq(groups.telegramChatId, chatId)).limit(1);
		return group[0]?.countryCode;
	} catch (error) {
		console.error('Error getting group country code:', error);
		return undefined;
	}
}

/**
 * 处理成员离开事件（可选功能）
 */
export async function handleMemberLeft(ctx: Context): Promise<void> {
	try {
		// 检查是否是群组消息
		if (ctx.chat?.type !== 'supergroup') {
			return;
		}

		const leftMember = ctx.message?.left_chat_member;
		if (!leftMember || leftMember.is_bot) {
			return;
		}

		// 可以在这里添加成员离开的处理逻辑
		// 例如：更新数据库中的成员计数
		console.log(`Member ${leftMember.first_name} left group ${ctx.chat.id}`);
	} catch (error) {
		console.error('Error handling member left:', error);
	}
}

/**
 * 注册用户引导相关的事件处理器
 */
export function registerUserOnboardingHandlers(bot: any): void {
	// 监听新成员加入
	bot.on('message:new_chat_members', handleNewMember);

	// 监听成员离开（可选）
	bot.on('message:left_chat_member', handleMemberLeft);

	console.log('✅ User onboarding handlers registered');
}
