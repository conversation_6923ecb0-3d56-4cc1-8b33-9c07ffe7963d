<script lang="ts">
	import RangeSlider from './RangeSlider.svelte';
	import { unitSettingsStore } from '$lib/stores/settingsStore'; // ✅ 1. 导入全局单位设置 Store
	import {
		cmToTotalInches,
		totalInchesToCm,
		cmRangeToInchesRange,
		formatFeetInches,
		cmToFeetInches
	} from '$utils/ui/unitConversions';

	type Props = {
		value?: number | null;
		label: string;
		minCm?: number;
		maxCm?: number;
		disabled?: boolean;
		onInput: (newValue: number | null) => void;
	};

	let { value = 175, label, minCm = 140, maxCm = 210, disabled = false, onInput }: Props = $props();

	// ✅ 2. 直接从全局 store 中派生出当前应该使用的单位
	const currentUnit = $derived($unitSettingsStore.height);

	// ✅ 3. 所有派生逻辑现在都依赖于这个全局的 currentUnit，组件自身不再有单位状态
	const sliderConfig = $derived.by(() => {
		if (currentUnit === 'cm') return { min: minCm, max: maxCm };
		return cmRangeToInchesRange(minCm, maxCm);
	});
	const sliderDisplayValue = $derived(currentUnit === 'cm' ? value : cmToTotalInches(value));
	const formattedDisplayValue = $derived(
		currentUnit === 'cm' ? `${value ?? ''} cm` : formatFeetInches(cmToFeetInches(value))
	);

	// ✅ 4. 输入处理函数也依赖全局 store 来进行转换
	function handleSliderInput(newValueFromSlider: number | null) {
		if (newValueFromSlider === null) {
			onInput(null);
			return;
		}
		const newCmValue =
			currentUnit === 'cm' ? newValueFromSlider : Math.round(totalInchesToCm(newValueFromSlider));
		onInput(newCmValue);
	}
</script>

<!-- ✅ 模板被大大简化，移除了所有与单位切换相关的 UI -->
<RangeSlider
	{label}
	min={sliderConfig.min}
	max={sliderConfig.max}
	step={1}
	content={formattedDisplayValue}
	{disabled}
	value={sliderDisplayValue}
	onInput={handleSliderInput}
/>
