// src/lib/server/db/schema/_enums.ts
// CipherX TMA - PostgreSQL 枚举类型定义
// 这些枚举必须与 drizzle/0000_init_database.sql 中的定义完全一致

import { pgEnum } from 'drizzle-orm/pg-core';
import {
	ORIENTATION_VALUES,
	BODY_TYPE_VALUES,
	PRESENTATION_STYLE_VALUES,
	RELATIONSHIP_STATUS_VALUES
} from '$lib/constants/dbTypeEnums';
/**
 * 性取向枚举
 */
export const orientationEnum = pgEnum('orientation_enum', ORIENTATION_VALUES);

/**
 * 身体类型枚举
 */
export const bodyTypeEnum = pgEnum('body_type_enum', BODY_TYPE_VALUES);

/**
 * 外表风格枚举
 */
export const presentationStyleEnum = pgEnum('presentation_style_enum', PRESENTATION_STYLE_VALUES);

/**
 * 关系状态枚举
 */
export const relationshipStatusEnum = pgEnum(
	'relationship_status_enum',
	RELATIONSHIP_STATUS_VALUES
);

/**
 * 匹配状态枚举
 */
export const matchStatusEnum = pgEnum('match_status_enum', [
  'liked',
  'matched',
  'blocked'
]);

/**
 * 积分交易类型枚举
 */
export const pointTransactionTypeEnum = pgEnum('point_transaction_type_enum', [
  'registration_bonus',
  'daily_check_in',
  'profile_completion_bonus',
  'group_subscription_fee',
  'super_like_cost',
  'invite_bonus',
  'top_up',
  'system_adjustment'
]);

/**
 * 群组角色枚举
 */
export const groupRoleEnum = pgEnum('group_role_enum', [
  'member',
  'moderator',
  'admin'
]);

/**
 * Kink 定义类型枚举
 */
export const kinkDefinitionTypeEnum = pgEnum('kink_definition_type_enum', [
  'role',
  'interest'
]);

/**
 * Bot 状态枚举
 */
export const botStatusEnum = pgEnum('bot_status_enum', [
  'active',
  'inactive',
  'maintenance'
]);


/**
 * 群组状态枚举
 */
export const groupStatusEnum = pgEnum('group_status_enum', [
  'active',    // 活跃，正常可见
  'archived',  // 已归档，可能在TMA中不显示
  'private'    // 私有，仅限特定人群可见
]);

/**
 * 群组成员状态枚举
 */
export const groupMembershipStatusEnum = pgEnum('group_membership_status_enum', [
  'active',    // 活跃
  'muted',     // 因积分不足等原因被禁言
  'banned'     // 被管理员封禁
]);


/**
 * 话题状态枚举
 */
export const topicStatusEnum = pgEnum('topic_status_enum', [
  'active',
  'archived',
  'read_only'
]);

/**
 * 话题类型枚举
 */
export const topicTypeEnum = pgEnum('topic_type_enum', [
  'standard',    // 标准话题 (公告、闲聊等)
  'province',    // 省份/州话题
  'event',       // 活动话题
  'temporary',   // 临时话题
  'custom'       // 管理员自定义
]);




// 导出所有枚举类型，供其他模块使用
export type OrientationType = typeof orientationEnum.enumValues[number];
export type BodyType = typeof bodyTypeEnum.enumValues[number];
export type PresentationStyle = typeof presentationStyleEnum.enumValues[number];
export type RelationshipStatus = typeof relationshipStatusEnum.enumValues[number];
export type MatchStatus = typeof matchStatusEnum.enumValues[number];
export type PointTransactionType = typeof pointTransactionTypeEnum.enumValues[number];
export type GroupRole = typeof groupRoleEnum.enumValues[number];
export type KinkDefinitionType = typeof kinkDefinitionTypeEnum.enumValues[number];
export type BotStatus = typeof botStatusEnum.enumValues[number];
export type GroupStatus = typeof groupStatusEnum.enumValues[number];
export type GroupMembershipStatus = typeof groupMembershipStatusEnum.enumValues[number];
export type TopicStatus = typeof topicStatusEnum.enumValues[number];
export type TopicType = typeof topicTypeEnum.enumValues[number];
