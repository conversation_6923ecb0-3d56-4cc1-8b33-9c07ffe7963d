<!-- src/routes/+layout.svelte -->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { App } from 'konsta/svelte';
	import { cloudStorage, themeParams } from '@telegram-apps/sdk-svelte'; // 仅导入 themeParams
	import * as m from '$lib/paraglide/messages';
	import '../app.css';
	import { Loader } from '$components';
	import { devLog } from '$utils/dev/devLogger';
	import {  tgSdkInitializedStore, unitSettingsStore, type UnitSettings } from '$lib/stores';
	import type { LayoutProps } from './$types';
	import { afterNavigate, beforeNavigate } from '$app/navigation';
	import { debounce } from '../lib/utils/ui/debounce';

	let { children}: LayoutProps = $props();
	let themeApplied = $state(false);
	let unbindCssVars: VoidFunction | undefined;
	let unlistenThemeChange: VoidFunction | undefined; // 如果有额外监听，用于清理

	// 辅助函数：将 camelCase 字符串转换为 kebab-case
	function toKebabCase(str: string): string {
		return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
	}

	// beforeNavigate 会在每次客户端导航开始前被调用
	beforeNavigate(({ from, to, type, cancel }) => {
		console.log('%c====== NAVIGATION STARTING ======', 'color: blue; font-weight: bold;');
		console.log(`From: ${from?.url.pathname ?? 'N/A (initial load)'}`);
		console.log(`To:   ${to?.url.pathname ?? 'N/A (external site)'}`);
		console.log(`Type: ${type}`);
		console.log('================================');
	});

	// afterNavigate 在导航成功后调用，可以帮我们确认导航是否真的完成了
	afterNavigate(({ from, to, type }) => {
		console.log('%c====== NAVIGATION COMPLETE ======', 'color: green; font-weight: bold;');
		console.log(`Landed on: ${to?.url.pathname ?? 'N/A'}`);
		console.log('=================================');
	});

	function applyTelegramTheme() {
		if (themeApplied) return;

		try {
			if (themeParams.mountSync.isAvailable()) {
				themeParams.mountSync();
			} else {
				devLog({ log: 'Theme parameters mount method not available.' });
				themeApplied = true;
				return;
			}

			if (themeParams.bindCssVars.isAvailable()) {
				themeParams.bindCssVars();
			} else {
				devLog({ log: 'TMA theme features are not available. Using default theme.' });
				themeApplied = true;
				return;
			}

			// 核心：绑定 CSS 变量。
			// 我们自定义变量名，以便在 Tailwind 中使用 `--tg-theme-bg-color` 这种形式。
			unbindCssVars = themeParams.bindCssVars((key) => `--tg-theme-${toKebabCase(key)}`);

			devLog({ log: '✅ TMA theme applied successfully.' });
			themeApplied = true;
		} catch (e) {
			if (e instanceof Error) {
				if (e.name === 'CSSVarsBoundError') {
					devLog({ log: 'TMA theme CSS variables already bound, skipping.' });
					themeApplied = true;
				} else if (e.name === 'FunctionNotAvailableError') {
					devLog({ log: `TMA theme function not available: ${e.message}` });
					themeApplied = true;
				} else {
					console.warn('⚠️ Failed to apply TMA theme, falling back to default styles.', e);
				}
			} else {
				console.warn('⚠️ Unknown error applying TMA theme.', e);
			}
		}
	}
	let settingsInitialized = $state(false);

	const saveSettingsToCloud = debounce((settings: UnitSettings) => {
		try {
			console.log('Saving unit settings to CloudStorage...', settings);
			cloudStorage.setItem('unit_settings', JSON.stringify(settings));
		} catch(e) {
			console.error('Failed to save settings to CloudStorage:', e);
		}
	}, 500);

	onMount(async () => {
		if ($tgSdkInitializedStore && !themeApplied) {
			applyTelegramTheme();
		}
		if (settingsInitialized) return;
		settingsInitialized = true;

		try {
			console.log('Reading unit settings from CloudStorage...');
			const savedSettings = await cloudStorage.getItem('unit_settings');
			if (savedSettings) {
				const parsedSettings: UnitSettings = JSON.parse(savedSettings);
				unitSettingsStore.set(parsedSettings); // 用云端数据更新本地 store
				console.log('Unit settings loaded from cloud:', parsedSettings);
			}
		} catch (e) {
			console.error('Failed to load settings from CloudStorage:', e);
		}
	});

		$effect(() => {
		// 只有在初始化之后，才开始监听 store 的变化并保存
		if (settingsInitialized) {
			saveSettingsToCloud($unitSettingsStore);
		}
	});

	onDestroy(() => {
		if (unbindCssVars) {
			unbindCssVars();
			devLog({ log: 'Theme CSS variables unbound.' });
		}
		if (unlistenThemeChange) {
			unlistenThemeChange();
			devLog({ log: 'Theme change listener unbound.' });
		}
		if (themeParams.isMounted()) {
			themeParams.unmount();
			devLog({ log: 'Theme parameters unmounted.' });
		}
	});
</script>

<svelte:head>
	<title>{m.app_title()}</title>
	<meta name="description" content="CipherX - A new community platform" />
</svelte:head>

{#if !$tgSdkInitializedStore}
	<Loader />
{:else}
	<App safeAreas theme="ios">
		{@render children()}
	</App>
{/if}
