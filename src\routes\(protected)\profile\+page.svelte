<!-- src/routes/(protected)/profile/+page.svelte -->
<script lang="ts">
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		List,
		ListItem,
		Button,
		Card,
		Badge
	} from 'konsta/svelte';
	import { goto } from '$app/navigation';
	import * as m from '$lib/paraglide/messages';
	import { KINK_CATEGORY_BITMASK, type KinkRatingValue } from '$lib/constants/kinks';
	import ProfileCompletenessIndicator from '$lib/components/ui/ProfileCompletenessIndicator.svelte';
	import { getRatingBadgeColor, getRatingLabel } from '$utils/ui/kinkRatings.js';
	import Avatar from '$lib/components/ui/Avatar.svelte';
	import type { PageProps } from './$types';
	import { displayUserProfileStore } from '$lib/stores';

	let { data }: PageProps = $props();

	// 用户资料数据
	const userProfile = data.userProfile;

	// 跳转到编辑页面
	function editProfile() {
		$displayUserProfileStore = userProfile;
		goto('/onboarding');
	}

	// 格式化显示函数
	function formatLocation() {
		const parts = [];
		if (userProfile.city) parts.push(userProfile.city);
		if (userProfile.provinceCode) parts.push(userProfile.provinceCode);
		if (userProfile.countryCode) parts.push(userProfile.countryCode);
		return parts.join(', ') || m.routes_profile_location_not_set();
	}

	function formatPhysicalInfo() {
		const parts = [];
		if (userProfile.age) parts.push(`${userProfile.age} ${m.common_unit_years()}`);
		if (userProfile.heightCm) parts.push(`${userProfile.heightCm}cm`);
		if (userProfile.weightKg) parts.push(`${userProfile.weightKg}kg`);
		return parts.join(' • ') || m.routes_profile_physical_not_set();
	}

	function getSelectedKinkRoles() {
		if (!userProfile.kinkCategoryBitmask) return [];

		return Object.entries(KINK_CATEGORY_BITMASK)
			.filter(([_, bitmask]) => (Number(userProfile.kinkCategoryBitmask) & bitmask) !== 0)
			.map(([role, _]) => role);
	}

	function getKinkRatingsCount() {
		return userProfile.kinkRatings ? Object.keys(userProfile.kinkRatings).length : 0;
	}

	const selectedKinkRoles = $derived(getSelectedKinkRoles());
	const kinkRatingsCount = $derived(getKinkRatingsCount());
</script>

<Page>
	<Navbar title={m.routes_profile_title()}>
		<div slot="right">
			<Button clear onclick={editProfile}>
				{m.routes_profile_button_edit_profile()}
			</Button>
		</div>
	</Navbar>

	<!-- Profile Header Card -->
	<Block class="!py-2">
		<Card class="!p-3">
			<div class="flex items-center space-x-4">
				<!-- Avatar -->
				<Avatar telegramUrl={userProfile.profileImageUrl} name={userProfile.nickname} size={80} />

				<!-- Basic Info -->
				<div class="flex-1">
					<h2 class="text-xl font-bold text-gray-900">
						{userProfile.nickname}
					</h2>
					<p class="mt-1 text-sm text-gray-600">{formatLocation()}</p>
					<p class="text-sm text-gray-600">{formatPhysicalInfo()}</p>

					<!-- Kink Map Code -->
					<div class="mt-2">
						<Badge class="bg-purple-100 text-xs text-purple-800">
							{m.routes_profile_kink_map_code()}: {userProfile.kinkMapCode}
						</Badge>
					</div>
				</div>
			</div>
		</Card>
	</Block>
	<ProfileCompletenessIndicator profileData={userProfile} />
	<!-- Account Information -->
	<BlockTitle>{m.routes_profile_account_info_title()}</BlockTitle>
	<List strong inset>
		<ListItem
			title={m.routes_profile_trust_score()}
			after={`${userProfile.trustScore || 100}/100`}
		/>
		<ListItem
			title={m.routes_profile_member_since()}
			after={new Date(userProfile.createdAt).toLocaleDateString()}
		/>
		<ListItem
			title={m.routes_profile_last_active()}
			after={new Date(userProfile.lastActiveAt).toLocaleDateString()}
		/>
	</List>

	<!-- Bio Section -->
	{#if userProfile.bio}
		<BlockTitle>{m.routes_profile_bio_title()}</BlockTitle>
		<Block strong inset>
			<p class="leading-relaxed text-gray-700">{userProfile.bio}</p>
		</Block>
	{/if}

	<!-- Basic Information -->
	<BlockTitle>{m.routes_profile_basic_info_title()}</BlockTitle>
	<List strong inset>
		<ListItem
			title={m.routes_profile_label_orientation()}
			after={userProfile.orientation
				? (m as any)[`data_orientation_${userProfile.orientation}`]?.() || userProfile.orientation
				: m.routes_profile_not_set()}
		/>
		<ListItem
			title={m.routes_profile_label_body_type()}
			after={userProfile.bodyType
				? (m as any)[`data_body_type_${userProfile.bodyType}`]?.() || userProfile.bodyType
				: m.routes_profile_not_set()}
		/>
		<ListItem
			title={m.routes_profile_label_presentation_style()}
			after={userProfile.presentationStyle
				? (m as any)[`data_presentation_style_${userProfile.presentationStyle}`]?.() ||
					userProfile.presentationStyle
				: m.routes_profile_not_set()}
		/>
		<ListItem
			title={m.routes_profile_label_relationship_status()}
			after={userProfile.relationshipStatus
				? (m as any)[`data_relationship_status_${userProfile.relationshipStatus}`]?.() ||
					userProfile.relationshipStatus
				: m.routes_profile_not_set()}
		/>
	</List>

	<!-- Kink Profile Section -->
	<BlockTitle>{m.routes_profile_kink_profile_title()}</BlockTitle>
	<List strong inset>
		<ListItem
			title={m.routes_profile_kink_roles_title()}
			after={selectedKinkRoles.length > 0
				? `${selectedKinkRoles.length} ${m.routes_profile_roles_selected()}`
				: m.routes_profile_not_set()}
		/>
		<ListItem
			title={m.routes_profile_kink_interests_title()}
			after={kinkRatingsCount > 0
				? `${kinkRatingsCount} ${m.routes_profile_interests_rated()}`
				: m.routes_profile_not_set()}
		/>
	</List>

	<!-- Selected Kink Roles Display -->
	{#if selectedKinkRoles.length > 0}
		<BlockTitle>{m.routes_profile_selected_roles_title()}</BlockTitle>
		<Block strong inset>
			<div class="flex flex-wrap gap-2">
				{#each selectedKinkRoles as role}
					<Badge class="bg-blue-300 text-xs text-blue-800">
						{(m as any)[`data_kink_role_${role}`]?.() || role}
					</Badge>
				{/each}
			</div>
		</Block>
	{/if}

	<!-- Kink Interests Display -->
	{#if kinkRatingsCount > 0}
		<BlockTitle>{m.routes_profile_kink_interests_detail_title()}</BlockTitle>
		<Block strong inset>
			<div class="space-y-3">
				{#each Object.entries(userProfile.kinkRatings || {}) as [interest, rating]}
					{@const ratingNum = Number(rating) as KinkRatingValue}
					<div class="flex items-center justify-between">
						<div class="flex-1">
							<span class="text-sm font-medium text-gray-900">
								{(m as any)[`data_kink_interest_${interest}`]?.() || interest}
							</span>
						</div>
						<div class="flex items-center space-x-2">
							<span
								class="rounded-full px-2 py-1 text-xs font-medium {getRatingBadgeColor(ratingNum)}"
							>
								{getRatingLabel(ratingNum)}
							</span>
							<span class="text-sm text-gray-500">
								{ratingNum > 0 ? '+' : ''}{ratingNum}
							</span>
						</div>
					</div>
				{/each}
			</div>
		</Block>
	{/if}
</Page>
