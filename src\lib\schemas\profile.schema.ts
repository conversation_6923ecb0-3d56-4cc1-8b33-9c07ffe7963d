// src/lib/schemas/profile.schema.ts
import { z } from 'zod';
import {
	// 导入所有需要的原子验证规则
	createIdSchema,
	telegramUserIdSchema,
	tonWalletAddressSchema,
	kinkMapCodeSchema,
	nicknameSchema,
	telegramUsernameSchema,
	ageSchema,
	heightSchema,
	weightSchema,
	countryCodeSchema,
	provinceCodeSchema,
	citySchema,
	languageCodeSchema,
	bioSchema,
	urlSchema,
	kinkRatingsValueSchema
} from '$lib/schemas/common.schema';
import {
	ORIENTATION_VALUES,
	PRESENTATION_STYLE_VALUES,
	RELATIONSHIP_STATUS_VALUES,
	BODY_TYPE_VALUES
} from '$lib/constants/dbTypeEnums';
import { ID_PREFIX_USER } from '$lib/constants/prefixes';

// ====================================================================
// --- 模块一：用户可编辑的核心资料 Schema (The Editable Blueprint) ---
//    这是我们关于“用户可以自己修改的资料”的唯一事实来源。
// ====================================================================
export const editableProfileSchema = z.object({
	nickname: nicknameSchema.nullable(), // 允许在编辑时清空，但 action 中可以再校验
	age: ageSchema.nullable(),
	heightCm: heightSchema.nullable(),
	weightKg: weightSchema.nullable(),
	profileImageUrl: urlSchema.nullable(),
	languageCode: languageCodeSchema.nullable(),
	countryCode: countryCodeSchema.nullable(),
	provinceCode: provinceCodeSchema.nullable(),
	city: citySchema.nullable(),
	bio: bioSchema.nullable(),
	orientation: z.enum(ORIENTATION_VALUES).nullable(),
	bodyType: z.enum(BODY_TYPE_VALUES).nullable(),
	presentationStyle: z.enum(PRESENTATION_STYLE_VALUES).nullable(),
	relationshipStatus: z.enum(RELATIONSHIP_STATUS_VALUES).nullable(),
	kinkCategoryBitmask: z.coerce.bigint().nullable(),
	kinkRatings: z.record(z.string(), kinkRatingsValueSchema).nullable()
});

// ====================================================================
// --- 模块二：服务器端管理的专属字段 Schema ---
// ====================================================================
const serverManagedSchema = z.object({
	id: createIdSchema(ID_PREFIX_USER),
	telegramUserId: telegramUserIdSchema,
	tonWalletAddress: tonWalletAddressSchema.nullable(),
	inviterId: createIdSchema(ID_PREFIX_USER).nullable(),
	kinkMapCode: kinkMapCodeSchema,
	telegramUsername: telegramUsernameSchema.nullable(),
	profileCompletenessScore: z.number().int(),
	trustScore: z.number().int(),
	vipLevel: z.number().int(),
	pointBalance: z.number().int(),
	isActive: z.boolean(),
	isBanned: z.boolean(),
	createdAt: z.date(),
	updatedAt: z.date(),
	lastActiveAt: z.date()
});

// ====================================================================
// --- 组合与派生：构建所有需要的数据形态 ---
// ====================================================================

// ✅ 1. 物理模型：通过合并两个模块，创建与数据库 1:1 对应的 schema
export const dbUserSchema = editableProfileSchema.merge(serverManagedSchema);

// ✅ 3. 公开资料展示视图：从完整的 dbUserSchema 中挑选出需要展示的字段
//    我们不再需要手动的 userProfileColumns 对象了，可以直接删除它！
export const displayUserProfileSchema = dbUserSchema.pick({
	id: true,
	telegramUserId: true,
	kinkMapCode: true,
	nickname: true,
	age: true,
	heightCm: true,
	weightKg: true,
	countryCode: true,
	provinceCode: true,
	city: true,
	bio: true,
	profileImageUrl: true,
	orientation: true,
	bodyType: true,
	presentationStyle: true,
	relationshipStatus: true,
	lastActiveAt: true,
	profileCompletenessScore: true,
	kinkRatings: true,
	kinkCategoryBitmask: true,
	vipLevel: true,
	isBanned: true,
	trustScore: true
});

// ✅ 2. Onboarding 表单视图：为 onboarding 表单创建一个精确的 schema
//    我们从用户可编辑的字段出发，并强化某些字段为必填。
export const onboardingFormSchema = editableProfileSchema.extend({
	nickname: nicknameSchema
});
// ✅ 4. 更新用户资料的视图：这是 action 或 API 接收的数据类型
//    它应该是可编辑字段的“部分可选”版本
export const updateUserProfileSchema = editableProfileSchema.partial();

export const profileForScoreSchema = dbUserSchema.pick({
	// ✅ 在这里列出 calculateProfileCompleteness 函数需要的所有字段
	// 例如：
	nickname: true,
	age: true,
	heightCm: true,
	weightKg: true,
	countryCode: true,
	provinceCode: true,
	city: true,
	bio: true,
	profileImageUrl: true,
	orientation: true,
	bodyType: true,
	presentationStyle: true,
	relationshipStatus: true,
	kinkCategoryBitmask: true,
	kinkRatings: true
});

// ====================================================================
// --- 类型导出 ---
// ====================================================================
export type DbUser = z.infer<typeof dbUserSchema>;
export type OnboardingFormData = z.infer<typeof onboardingFormSchema>;
export type DisplayUserProfile = z.infer<typeof displayUserProfileSchema>;
export type UpdateUserProfileData = z.infer<typeof updateUserProfileSchema>;
export type ProfileForScore = z.infer<typeof profileForScoreSchema>;
export type SerializedUserProfile = Omit<
	DisplayUserProfile,
	'telegramUserId' | 'kinkCategoryBitmask'
> & {
	telegramUserId: string;
	kinkCategoryBitmask: string | null;
};
