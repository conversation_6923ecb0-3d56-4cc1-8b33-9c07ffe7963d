// src/lib/schemas/bot.schema.ts
// CipherX TMA - Bot 管理相关验证规则

import { z } from 'zod';
import { createIdSchema, countryCodeSchema } from '$lib/schemas/common.schema.js';
import { botStatusEnum } from '$lib/server/db/schema/_enums';
import { ID_PREFIX_USER, ID_PREFIX_BOT } from '$lib/constants/prefixes.js';

/**
 * 创建新Bot记录时的验证 Schema
 */
export const createBotSchema = z.object({
	id: createIdSchema(ID_PREFIX_BOT),
	name: z.string().min(2, 'lib_validation_bot_name_too_short'),
	tokenAlias: z
		.string()
		.min(3, 'lib_validation_token_alias_too_short')
		.regex(/^[a-z0-9-]+$/, 'lib_validation_token_alias_format'),

	description: z.string().optional(),
	countryCode: countryCodeSchema.optional(),
	creatorId: createIdSchema(ID_PREFIX_USER)
});

/**
 * 更新Bot信息时的验证 Schema
 */
export const updateBotSchema = createBotSchema
	.pick({
		name: true,
		description: true,
		countryCode: true
	})
	.partial()
	.extend({
		botId: createIdSchema(ID_PREFIX_BOT),
		status: z.enum(botStatusEnum.enumValues).optional()
	});

// --- 导出Zod推断的TypeScript类型 ---
export type CreateBotData = z.infer<typeof createBotSchema>;
export type UpdateBotData = z.infer<typeof updateBotSchema>;
