<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { displayUserProfileStore } from '$stores';
	import { retrieveRawInitData } from '@telegram-apps/sdk-svelte';
	import { AUTHORIZATION_TMA_PREFIX } from '$lib/constants';
	import { Loader } from '$components';
	import { page } from '$app/state';
	import { devLog } from '$lib/utils';

	// 这个页面只在 onMount 中执行逻辑，然后立即跳转，
	// 所以它本身只需要显示一个加载状态。

	onMount(async () => {
		// onMount 只在客户端执行
		const user = $displayUserProfileStore;
		// 情况一：Store 中已有用户数据
		// 这意味着根布局的 load 函数已经通过 session cookie 成功获取了用户
		// 或者用户是从其他页面导航回了主页
		if (user) {
			// 我们信任这份数据，直接根据它进行跳转
			if (user.profileCompletenessScore < 50) {
				// 使用您的阈值
				goto('/onboarding', { replaceState: true });
			} else {
				goto('/profile', { replaceState: true });
			}
			return; // 结束逻辑
		} else {
			try {
				console.log('No session found, attempting TMA client-side authentication...');
				const initDataRaw = retrieveRawInitData();
				if (!initDataRaw) {
					// 如果不在TMA环境或拿不到initData，可以跳转到错误页或提示页
					goto('/auth-failed');
					return;
				}

				const response = await fetch('/api/auth/telegram', {
					method: 'POST',
					headers: {
						Authorization: `${AUTHORIZATION_TMA_PREFIX}${initDataRaw}`
					}
				});

				if (response.ok) {
					const resultData = await response.json();
					devLog({ resultData: { resultData } });
					// API 成功返回了完整的用户数据和 session cookie (已在后台设置)
					// 现在我们手动更新 store，并根据服务器返回的结果进行跳转
					displayUserProfileStore.set(resultData.userProfile);
					if (resultData.userNeedsOnboarding) {
						goto('/onboarding', { replaceState: true });
					} else {
						goto('/profile', { replaceState: true });
					}
				} else {
					// API 调用失败
					goto('/auth-failed');
				}
			} catch (e) {
				console.error('TMA client-side auth process failed:', e);
				goto('/auth-failed');
			}
		}
	});
</script>

<div class="flex h-screen items-center justify-center">
	<Loader />
</div>
