authHeader : tma user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22cipher<PERSON>%22%2C%22last_name%22%3A%22_ZH%22%2C%22username%22%3A%22cipherX_ZH_admin%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1751543157&signature=dK_yB_eFFexVEpl4tS-GBt0NK7Tet22OwnasL-G2ldI70oFFoRwHGtAUVOkxavfuSUEOghWWgdprViQh6naABQ&hash=cae26c402afb16e721cf204bfeb608085adc8bb158660a2c5c5152418347c84a
Backend - Extracted initDataRaw: user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22cipherX%22%2C%22last_name%22%3A%22_ZH%22%2C%22username%22%3A%22cipherX_ZH_admin%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1751543157&signature=dK_yB_eFFexVEpl4tS-GBt0NK7Tet22OwnasL-G2ldI70oFFoRwHGtAUVOkxavfuSUEOghWWgdprViQh6naABQ&hash=cae26c402afb16e721cf204bfeb608085adc8bb158660a2c5c5152418347c84a
Query: select "id", "telegram_user_id", "ton_wallet_address", "inviter_id", "kink_map_code", "nickname", "telegram_username", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "language_code", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "kink_category_bitmask", "kink_ratings", "profile_completeness_score", "trust_score", "vip_level", "point_balance", "is_active", "is_banned", "created_at", "updated_at", "last_active_at" from "users" "users" where "users"."telegram_user_id" = $1 limit $2 -- params: [6662549305, 1]
[UserService] Found existing user: usr_74yzcsqLXerkGFY
Query: update "users" set "telegram_username" = $1, "profile_image_url" = $2, "last_active_at" = $3 where "users"."id" = $4 returning "id", "telegram_user_id", "ton_wallet_address", "inviter_id", "kink_map_code", "nickname", "telegram_username", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "language_code", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "kink_category_bitmask", "kink_ratings", "profile_completeness_score", "trust_score", "vip_level", "point_balance", "is_active", "is_banned", "created_at", "updated_at", "last_active_at" -- params: ["cipherX_ZH_admin", "https://t.me/i/userpic/320/Ddepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg", "2025-07-03T11:54:59.386Z", "usr_74yzcsqLXerkGFY"]    
Query: insert into "sessions" ("id", "user_id", "expires_at") values ($1, $2, $3) -- params: ["kyrywbfg5j4lm5l4g2t2o6cqwvxujcfsvzlhuxup", "usr_74yzcsqLXerkGFY", "2025-08-02T11:54:59.399Z"]
11:54:59 [vite-plugin-svelte] src/lib/components/ui/CountrySelector.svelte:21:5 `statesOfCountry` is updated, but is not declared with `$state(...)`. Changing its value will not correctly trigger updates
https://svelte.dev/e/non_reactive_update
Query: select "keys"."id", "keys"."user_id", "keys"."hashed_password", "sessions"."id", "sessions"."user_id", "sessions"."expires_at" from "sessions" inner join "keys" on "sessions"."user_id" = "keys"."id" where "sessions"."id" = $1 -- params: ["kyrywbfg5j4lm5l4g2t2o6cqwvxujcfsvzlhuxup"]
authHeader : tma user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22cipherX%22%2C%22last_name%22%3A%22_ZH%22%2C%22username%22%3A%22cipherX_ZH_admin%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1751543157&signature=dK_yB_eFFexVEpl4tS-GBt0NK7Tet22OwnasL-G2ldI70oFFoRwHGtAUVOkxavfuSUEOghWWgdprViQh6naABQ&hash=cae26c402afb16e721cf204bfeb608085adc8bb158660a2c5c5152418347c84a
Backend - Extracted initDataRaw: user=%7B%22id%22%3A6662549305%2C%22first_name%22%3A%22cipherX%22%2C%22last_name%22%3A%22_ZH%22%2C%22username%22%3A%22cipherX_ZH_admin%22%2C%22language_code%22%3A%22zh-hans%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FDdepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg%22%7D&chat_instance=6891755669185758424&chat_type=private&auth_date=1751543157&signature=dK_yB_eFFexVEpl4tS-GBt0NK7Tet22OwnasL-G2ldI70oFFoRwHGtAUVOkxavfuSUEOghWWgdprViQh6naABQ&hash=cae26c402afb16e721cf204bfeb608085adc8bb158660a2c5c5152418347c84a
Query: select "id", "telegram_user_id", "ton_wallet_address", "inviter_id", "kink_map_code", "nickname", "telegram_username", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "language_code", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "kink_category_bitmask", "kink_ratings", "profile_completeness_score", "trust_score", "vip_level", "point_balance", "is_active", "is_banned", "created_at", "updated_at", "last_active_at" from "users" "users" where "users"."telegram_user_id" = $1 limit $2 -- params: [6662549305, 1]
[UserService] Found existing user: usr_74yzcsqLXerkGFY
Query: update "users" set "telegram_username" = $1, "profile_image_url" = $2, "last_active_at" = $3 where "users"."id" = $4 returning "id", "telegram_user_id", "ton_wallet_address", "inviter_id", "kink_map_code", "nickname", "telegram_username", "age", "height_cm", "weight_kg", "country_code", "province_code", "city", "language_code", "bio", "profile_image_url", "orientation", "body_type", "presentation_style", "relationship_status", "kink_category_bitmask", "kink_ratings", "profile_completeness_score", "trust_score", "vip_level", "point_balance", "is_active", "is_banned", "created_at", "updated_at", "last_active_at" -- params: ["cipherX_ZH_admin", "https://t.me/i/userpic/320/Ddepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg", "2025-07-03T11:55:06.896Z", "usr_74yzcsqLXerkGFY"]  
Query: insert into "sessions" ("id", "user_id", "expires_at") values ($1, $2, $3) -- params: ["ltmla55drwjzrcb5xrqzoio6vjwixh5y7yduw5r4", "usr_74yzcsqLXerkGFY", "2025-08-02T11:55:06.903Z"]
Query: select "keys"."id", "keys"."user_id", "keys"."hashed_password", "sessions"."id", "sessions"."user_id", "sessions"."expires_at" from "sessions" inner join "keys" on "sessions"."user_id" = "keys"."id" where "sessions"."id" = $1 -- params: ["ltmla55drwjzrcb5xrqzoio6vjwixh5y7yduw5r4"]
