import { paraglideVitePlugin } from '@inlang/paraglide-js';
import tailwindcss from '@tailwindcss/vite';
import devtoolsJson from 'vite-plugin-devtools-json';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import mkcert from 'vite-plugin-mkcert';
import { enhancedImages } from '@sveltejs/enhanced-img';
export default defineConfig({
	plugins: [
		enhancedImages(),
		mkcert(),
		tailwindcss(),
		sveltekit(),
		devtoolsJson(),
		paraglideVitePlugin({
			project: './project.inlang',
			outdir: './src/lib/paraglide'
		})
	],
	server: {
		https: {}, // ✅ 开启 HTTPS
		hmr: {
			host: '45c2-207-174-7-179.ngrok-free.app'
		},
		// 关键修正点：添加 server.fs.allow 配置
		fs: {
			// 将 pnpm 的全局 store 目录添加到许可名单中
			allow: [
				// 这里可以保留 Vite 默认的搜索根，或者直接添加您的项目根目录
				// 但最关键的是下面这一行
				'D:/dev-storage/pnpm/store/'
			]
		}
	}
});
