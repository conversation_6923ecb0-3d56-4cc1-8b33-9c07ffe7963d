<script lang="ts">
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		List,
		ListInput,
		ListItem,
		Button,
		Segmented
	} from 'konsta/svelte';
	import { getOnboardingFormContext } from '$lib/contexts/onboardingForm';
	import { formFieldProxy } from 'sveltekit-superforms';
	import {
		ChipGroup,
		RangeSlider,
		HeightRangeSlider,
		WeightRangeSlider,
		CountrySelector
	} from '$components';
	import * as m from '$lib/paraglide/messages';
	import { tgUserStore, unitSettingsStore } from '$stores'; // ✅ 导入单位设置 store
	import {
		bodyTypeItems,
		orientationItems,
		presentationStyleItems,
		relationshipStatusItems
	} from '$utils/ui/chipGroup.js';

	// 1. 从 Context 中获取完整的 superform 对象
	const superform = getOnboardingFormContext();

	// 2. 为了在模板中方便使用，解构出顶级状态
	const { form: formStore, constraints, submitting } = superform;

	// 3. ✅ 使用 formFieldProxy 为每个字段创建独立的、响应式正常的 proxy store
	const { value: nickname, errors: nicknameErrors } = formFieldProxy(superform, 'nickname');
	const { value: bodyType, errors: bodyTypeErrors } = formFieldProxy(superform, 'bodyType');
	const { value: age, errors: ageErrors } = formFieldProxy(superform, 'age');
	const { value: heightCm, errors: heightCmErrors } = formFieldProxy(superform, 'heightCm');
	const { value: weightKg, errors: weightKgErrors } = formFieldProxy(superform, 'weightKg');
	const { value: orientation, errors: orientationErrors } = formFieldProxy(
		superform,
		'orientation'
	);
	const { value: presentationStyle, errors: presentationStyleErrors } = formFieldProxy(
		superform,
		'presentationStyle'
	);
	const { value: relationshipStatus, errors: relationshipStatusErrors } = formFieldProxy(
		superform,
		'relationshipStatus'
	);

	const languageCode = $tgUserStore?.language_code;

	// 4. 接收父组件传来的页面切换函数
	let { onSwitchPage }: { onSwitchPage: () => void } = $props();
</script>

<!-- ✅ 5. 在模板中，所有绑定都使用这些新建的、简单的 proxy store，并放心使用 bind:value -->

<BlockTitle>{m.routes_onboarding_basic_profile_title()}</BlockTitle>
<List strong inset>
	<ListInput
		label={m.routes_onboarding_label_nickname()}
		name="nickname"
		bind:value={$nickname}
		error={$nicknameErrors?.[0]}
	/>
</List>

<BlockTitle>{m.routes_onboarding_physical_chars_title()}</BlockTitle>
<List strong inset>
	<ListItem header={m.routes_onboarding_label_unit_preference()} />
	<div class="grid grid-cols-2 gap-4 p-4">
		<div>
			<div class="mb-2 text-sm">{m.routes_onboarding_label_height()}</div>
			<Segmented>
				<button
					type="button"
					class="k-button {($unitSettingsStore.height ?? 'cm') === 'cm' ? 'k-button-active' : ''}"
					onclick={() => ($unitSettingsStore.height = 'cm')}
				>
					cm
				</button>
				<button
					type="button"
					class="k-button {($unitSettingsStore.height ?? 'cm') === 'ft-in'
						? 'k-button-active'
						: ''}"
					onclick={() => ($unitSettingsStore.height = 'ft-in')}
				>
					ft/in
				</button>
			</Segmented>
		</div>
		<div>
			<div class="mb-2 text-sm">{m.routes_onboarding_label_weight()}</div>
			<Segmented>
				<button
					type="button"
					class="k-button {($unitSettingsStore.weight ?? 'kg') === 'kg' ? 'k-button-active' : ''}"
					onclick={() => ($unitSettingsStore.weight = 'kg')}
				>
					kg
				</button>
				<button
					type="button"
					class="k-button {($unitSettingsStore.weight ?? 'kg') === 'lbs' ? 'k-button-active' : ''}"
					onclick={() => ($unitSettingsStore.weight = 'lbs')}
				>
					lbs
				</button>
			</Segmented>
		</div>
	</div>

	<ListItem
		title={m.routes_onboarding_label_body_type()}
		text={$bodyType ? m[`data_body_type_${$bodyType}`]() : m.common_placeholder_select()}
	/>
	<div class="p-4 pt-2">
		<ChipGroup items={bodyTypeItems} value={$bodyType} onChange={(newValue) => ($bodyType = newValue)} />
		{#if $bodyTypeErrors}<p class="mt-2 text-sm text-red-500">{$bodyTypeErrors[0]}</p>{/if}
	</div>
	<RangeSlider
		label={m.routes_onboarding_label_age()}
		min={18}
		max={99}
		step={1}
		value={$age}
		content={$age}
		onInput={(newValue) => ($age = newValue)}
	/>
	<HeightRangeSlider value={$heightCm} label={m.routes_onboarding_label_height()} onInput={(newValue) => ($heightCm = newValue)} />
	<WeightRangeSlider value={$weightKg}  label={m.routes_onboarding_label_weight()} onInput={(newValue) => ($weightKg = newValue)}/>
</List>

<BlockTitle>{m.routes_onboarding_advanced_profile_title()}</BlockTitle>
<Block strong inset>
	<ListItem
		title={m.routes_onboarding_label_orientation()}
		text={$orientation ? m[`data_orientation_${$orientation}`]() : m.common_placeholder_select()}
	/>
	<div class="p-4 pt-2"><ChipGroup items={orientationItems} value={$orientation} onChange={(newValue) => ($orientation = newValue)} /></div>
	<ListItem
		title={m.routes_onboarding_label_presentation_style()}
		text={$presentationStyle
			? m[`data_presentation_style_${$presentationStyle}`]()
			: m.common_placeholder_select()}
	/>
	<div class="p-4 pt-2">
		<ChipGroup items={presentationStyleItems} value={$presentationStyle} onChange={(newValue) => ($presentationStyle = newValue)}/>
	</div>
	<ListItem
		title={m.routes_onboarding_label_relationship_status()}
		text={$relationshipStatus
			? m[`data_relationship_status_${$relationshipStatus}`]()
			: m.common_placeholder_select()}
	/>
	<div class="p-4 pt-2">
		<ChipGroup items={relationshipStatusItems} value={$relationshipStatus} onChange={(newValue) => ($relationshipStatus = newValue)}/>
	</div>
</Block>

<Block class="p-4">
	<Button large tonal type="button" disabled={$submitting} onclick={onSwitchPage}>
		{m.routes_onboarding_tab_advanced()}
	</Button>
</Block>
