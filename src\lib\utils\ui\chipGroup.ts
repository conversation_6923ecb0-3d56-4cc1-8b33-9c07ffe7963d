import {
	BODY_TYPE_VALUES,
	ORIENTATION_VALUES,
	PRESENTATION_STYLE_VALUES,
	RELATIONSHIP_STATUS_VALUES
} from '$lib/constants/dbTypeEnums';
import { m } from '$lib/paraglide/messages';

export const bodyTypeItems = BODY_TYPE_VALUES.map((value) => ({
	value,
	label: m[`data_body_type_${value}`]()
}));

// Prepare items for advanced profile ChipGroups
export const orientationItems = ORIENTATION_VALUES.map((value) => ({
	value,
	label: m[`data_orientation_${value}`]()
}));

export const presentationStyleItems = PRESENTATION_STYLE_VALUES.map((value) => ({
	value,
	label: m[`data_presentation_style_${value}`]()
}));

export const relationshipStatusItems = RELATIONSHIP_STATUS_VALUES.map((value) => ({
	value,
	label: m[`data_relationship_status_${value}`]()
}));
