import { parse } from 'devalue';
import { get } from 'svelte/store';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { displayUserProfileStore } from '$lib/stores';
import { onboardingFormSchema, type DisplayUserProfile } from '$lib/schemas/profile.schema';
import type { PageLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load: PageLoad = async ({ fetch, parent }) => {
	// 1. 首先，确保我们已经通过根布局知道了用户是谁。
	//    如果用户未登录，parent() 内部的逻辑（或 hooks）应该已经处理了重定向。
	await parent();

	let finalUserData: DisplayUserProfile | undefined;

	// 2. 检查客户端缓存（我们的“冰箱”）
	const userFromStore = get(displayUserProfileStore);

	if (userFromStore) {
		// ✅ 缓存命中！这是从 /profile 等页面导航过来的情况。
		finalUserData = userFromStore;
	} else {
		// ✅ 缓存未命中！这是直接访问或刷新页面的情况。
		//    我们必须从服务器获取最新的、完整的用户数据。
		// 主动“呼叫服务员”（我们创建的独立的 API 端点）
		const response = await fetch('/api/profile');
		if (response.ok) {
			finalUserData = parse(await response.text());
		} else {
			// 如果 API 调用失败，这是一个严重的错误，应该通知用户
			console.error('Failed to fetch profile data for onboarding.', response.statusText);
			throw error(response.status, 'Could not load your profile data.');
		}
	}

	// 3. 无论数据来自何处（缓存或API），都在这里用它来创建最终的 form 对象
	const dataForForm = finalUserData
		? { ...finalUserData, nickname: finalUserData.nickname ?? undefined }
		: undefined;

	const form = await superValidate(dataForForm, zod(onboardingFormSchema), {
		errors: false
	});
	return { form };
};
