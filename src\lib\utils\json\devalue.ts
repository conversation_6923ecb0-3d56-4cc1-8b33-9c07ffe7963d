import * as devalue from 'devalue';
/**
 * 创建一个能够安全序列化 BigInt, Date 等复杂类型的 JSON 响应。
 * 它使用 devalue 替代了原生的 JSON.stringify。
 * @param data 要发送的数据对象。
 * @param init Response 的初始化选项，如 status, headers。
 * @returns 一个标准的 Response 对象。
 */
export function superJson(data: any, init?: ResponseInit): Response {
	const headers = new Headers(init?.headers);
	headers.set('content-type', 'application/x-devalue');
	// 使用 devalue.stringify 来进行序列化
	return new Response(devalue.stringify(data), {
		...init,
		headers
	});
}
