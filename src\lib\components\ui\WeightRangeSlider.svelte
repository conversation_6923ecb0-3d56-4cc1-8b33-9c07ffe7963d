<script lang="ts">
	import RangeSlider from './RangeSlider.svelte';
	import { unitSettingsStore } from '$lib/stores/settingsStore'; // ✅ 1. 导入全局单位设置 Store
	import {
		kgToLbs,
		lbsToKg,
		kgRangeToLbsRange,
		isValidWeightKg
	} from '$utils/ui/unitConversions';

	type Props = {
		value?: number | null;
		label: string;
		minKg?: number;
		maxKg?: number;
		disabled?: boolean;
		onInput: (newValue: number | null) => void;
	};

	let {
		value = 70,
		label,
		minKg = 40,
		maxKg = 220,
		disabled = false,
		onInput
	}: Props = $props();

	// ✅ 2. 直接从全局 store 中派生出当前应该使用的单位
	const currentUnit = $derived($unitSettingsStore.weight);

	// ✅ 3. 所有派生逻辑依赖全局 store
	const sliderConfig = $derived.by(() => {
		if (currentUnit === 'kg') return { min: minKg, max: maxKg };
		return kgRangeToLbsRange(minKg, maxKg);
	});
	const sliderDisplayValue = $derived(currentUnit === 'kg' ? value : kgToLbs(value));
	const formattedDisplayValue = $derived(
		currentUnit === 'kg' ? `${value ?? ''} kg` : `${kgToLbs(value) ?? ''} lbs`
	);

	// ✅ 4. 输入处理函数依赖全局 store
	function handleSliderInput(newValueFromSlider: number | null) {
		if (newValueFromSlider === null) {
			onInput(null);
			return;
		}
		const newKgValue = currentUnit === 'kg' ? newValueFromSlider : lbsToKg(newValueFromSlider);
		if (isValidWeightKg(newKgValue)) {
			onInput(newKgValue);
		}
	}
</script>

<RangeSlider
	{label}
	min={sliderConfig.min}
	max={sliderConfig.max}
	step={1}
	content={formattedDisplayValue}
	{disabled}
	value={sliderDisplayValue}
	onInput={handleSliderInput}
/>
