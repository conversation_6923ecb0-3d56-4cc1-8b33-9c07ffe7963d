-- CipherX TMA Project - Genesis Schema Script v1.0
-- This script creates the entire database structure from scratch.
-- Execution Order: ENUMs -> Core Tables -> Indexes -> Triggers

BEGIN; -- Start a transaction to ensure all or nothing succeeds

-- =================================================================
-- SECTION 1: ENUM TYPE DEFINITIONS (基本材料)
-- =================================================================

CREATE TYPE orientation_enum AS ENUM ('straight', 'gay', 'lesbian', 'bisexual', 'asexual', 'demisexual', 'pansexual', 'queer', 'fluid', 'other', 'prefer_not_to_say');
CREATE TYPE body_type_enum AS ENUM ('male_body', 'female_body', 'other_body_type', 'prefer_not_to_say');
CREATE TYPE presentation_style_enum AS ENUM ('conventional_masculine', 'rugged_masculine', 'feminine', 'androgynous_neutral', 'other', 'prefer_not_to_say');
CREATE TYPE relationship_status_enum AS ENUM ('single', 'in_a_relationship', 'complicated', 'open_relationship', 'married', 'polyamorous', 'other', 'prefer_not_to_say');
CREATE TYPE match_status_enum AS ENUM ('liked', 'matched', 'blocked');
CREATE TYPE point_transaction_type_enum AS ENUM ('registration_bonus', 'daily_check_in', 'profile_completion_bonus', 'group_subscription_fee', 'super_like_cost', 'invite_bonus', 'top_up', 'system_adjustment');
CREATE TYPE group_role_enum AS ENUM ('member', 'moderator', 'admin');
CREATE TYPE kink_definition_type_enum AS ENUM ('role', 'interest');

-- =================================================================
-- SECTION 2: TABLE DEFINITIONS (地基与楼层)
-- =================================================================

-- 2.1 USERS (身份基石)
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    telegram_user_id BIGINT UNIQUE NOT NULL,
    ton_wallet_address TEXT UNIQUE,
    inviter_id TEXT REFERENCES users(id) ON DELETE SET NULL,
    kink_map_code TEXT UNIQUE NOT NULL,
    nickname TEXT NOT NULL,
    telegram_username TEXT,
    age INT,
    height_cm INT,
    weight_kg INT,
    country_code TEXT,
    province_code TEXT,
    city TEXT,
    language_code TEXT,
    bio TEXT,
    profile_image_url TEXT,
    orientation orientation_enum,
    body_type body_type_enum,
    presentation_style presentation_style_enum,
    relationship_status relationship_status_enum,
    kink_category_bitmask BIGINT ,
    kink_ratings JSONB ,
    profile_completeness_score INT NOT NULL DEFAULT 0,
    trust_score INT NOT NULL DEFAULT 100,
    vip_level INT NOT NULL DEFAULT 0,
    point_balance INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_banned BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2.2 LUCIA AUTH TABLES (认证双子星)
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL
);

CREATE TABLE keys (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    hashed_password TEXT
);

-- `groups` 表，最终版
-- 它存储社群的元数据，是 CaaS 模式的核心
CREATE TABLE groups (
    -- 核心ID，由程序生成 (nanoid)，永不改变，用于所有内部数据库关联
    id TEXT PRIMARY KEY,

    -- 人类可读的、用于URL和管理的唯一标识符，支持水平扩展
    -- 示例: 'usa-ca-men-seeking-men-01'
    slug TEXT UNIQUE NOT NULL,
    
    -- 与 Telegram 群组连接的桥梁
    telegram_chat_id BIGINT UNIQUE NOT NULL,
    name_en TEXT NOT NULL,
    name_zh TEXT,
    -- 国际化的、由管理员自定义的描述内容
    description_en TEXT,
    description_zh TEXT,

    -- 用于筛选和逻辑判断的结构化数据
    country_code TEXT NOT NULL,         -- e.g., 'US', 'CN'
    province_code TEXT,                 -- e.g., 'CA', 'NY' (国家级群组此字段为 NULL)
    theme TEXT NOT NULL,                -- e.g., 'men-seeking-men' (来自 constants)

    -- 管理信息
    creator_id TEXT NOT NULL REFERENCES users(id),
    member_count INT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
-- 2.4 GROUP MEMBERSHIPS (社区会员名册)
CREATE TABLE group_memberships (
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    group_id TEXT NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    role group_role_enum NOT NULL DEFAULT 'member',
    subscription_expires_at TIMESTAMPTZ NOT NULL,
    joined_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, group_id)
);

-- 2.5 MATCHES (社交图谱连接线)
CREATE TABLE matches (
    actor_user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    target_user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status match_status_enum NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (actor_user_id, target_user_id),
    CONSTRAINT chk_no_self_match CHECK (actor_user_id <> target_user_id)
);

-- 2.6 POINT TRANSACTIONS (经济系统流水账本)
CREATE TABLE point_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount INT NOT NULL,
    type point_transaction_type_enum NOT NULL,
    description TEXT,
    reference_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2.7 KINK DEFINITIONS (Kink 数据字典)
CREATE TABLE kink_definitions (
    id TEXT PRIMARY KEY,
    type kink_definition_type_enum NOT NULL,
    name_en TEXT NOT NULL,
    name_zh TEXT,
    description_en TEXT,
    description_zh TEXT,
    warning_en TEXT,
    warning_zh TEXT
);


-- `content_templates` 表，用于存储动态运营内容的模板
-- 这是您应用的“内置CMS”核心
CREATE TABLE content_templates (
    -- 模板的唯一标识符，由程序调用，例如: 'welcome_new_group'
    id TEXT PRIMARY KEY,

    -- 在后台给运营人员看，解释这个模板用在何处
    description TEXT,
    
    -- 为每一种支持的语言创建一个模板内容字段
    -- 模板中可使用 {placeholder} 语法，由后端程序动态替换
    content_en TEXT,
    content_zh TEXT,
    content_ja TEXT,

    -- 审计字段
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);



-- =================================================================
-- SECTION 3: INDEXES (性能加速器)
-- =================================================================

CREATE INDEX idx_users_geo ON users (country_code, province_code);
CREATE INDEX idx_sessions_user_id ON sessions (user_id);
CREATE INDEX idx_keys_user_id ON keys (user_id);
CREATE INDEX idx_groups_filter ON groups (country_code, theme);
CREATE INDEX idx_matches_incoming_interactions ON matches (target_user_id, status);
CREATE INDEX idx_point_transactions_user_id ON point_transactions (user_id);

-- =================================================================
-- SECTION 4: TRIGGERS (自动化助手)
-- =================================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- (可选，但推荐) 为这张表也创建一个自动更新 updated_at 的触发器
CREATE TRIGGER trigger_content_templates_updated_at
BEFORE UPDATE ON content_templates
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

COMMIT; -- End the transaction