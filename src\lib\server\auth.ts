// src/lib/server/auth.ts
import { Lucia } from 'lucia';
import { DrizzlePostgreSQLAdapter } from '@lucia-auth/adapter-drizzle';
import { db } from '$lib/server/db';
import { sessions, users } from '$lib/server/db/schema';
import type { UserAttributes } from '$lib/types/authType';

const adapter = new DrizzlePostgreSQLAdapter(db, sessions, users);

export const lucia = new Lucia(adapter, {
	sessionCookie: {
		// ✅ 关键修复：为TMA环境强制使用HTTPS安全配置
		attributes: {
			// `secure` 必须为 true
			secure: true,
			// `SameSite=None` 允许跨站发送Cookie
			sameSite: 'none'
			// @ts-ignore
			// partitioned: true
		}
	},
	getUserAttributes: (attributes): UserAttributes => {
		return {
			telegramUserId: attributes.telegramUserId,
			nickname: attributes.nickname,
			kinkMapCode: attributes.kinkMapCode,
			profileCompletenessScore: attributes.profileCompletenessScore
		};
	}
});

declare module 'lucia' {
	interface Register {
		Lucia: typeof lucia;
		DatabaseUserAttributes: UserAttributes;
	}
}

