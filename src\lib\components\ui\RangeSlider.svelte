<script lang="ts">
    import { Range, ListItem, BlockTitle } from 'konsta/svelte';
    import type { ComponentProps } from 'svelte';

    type Props = {
        value?: number | null; // This will now be a regular prop, receiving value from parent
        label: string;
        min: number;
        max: number;
        step?: number;
        content?: string | number | null;
        // Add an explicit onInput handler for RangeSlider
        onInput: (newValue: number | null) => void;
    } & Omit<ComponentProps<Range>, 'value' | 'min' | 'max' | 'step'>;

    let {
        value, // No $bindable here! This value just reflects the prop from parent
        label,
        min,
        max,
        step = 1,
        content = '',
        onInput, // The onInput prop from RangeSlider's parent
        ...restProps
    }: Props = $props();

    // Use a $state variable to internally manage the slider's current value
    // Initialize it with the prop value, but subsequent updates come from the slider itself
    let internalSliderValue = $derived(value);

    // Handle input from the Konsta Range component
    function handleKonstaRangeInput(event: Event) {
        const newValue = (event.target as HTMLInputElement).valueAsNumber;
        internalSliderValue = newValue; // Update internal state
        onInput(newValue); // Emit event to RangeSlider's parent
    }
</script>

<div>
    <BlockTitle class="flex justify-between">
        <span>{label}</span>
        <span class="font-semibold">{content}</span>
    </BlockTitle>
    <ListItem innerClass="flex flex-col space-y-2 rtl:space-y-reverse">
        <div class="flex w-full justify-between">
            <span class="text-sm opacity-50">{min}</span>
            <span class="text-sm opacity-50">{max}</span>
        </div>
        <Range
            {...restProps}
            {min}
            {max}
            {step}
            value={internalSliderValue} 
            onInput={handleKonstaRangeInput} 
        />
    </ListItem>
</div>