/* src/app.pcss */

/* 确保这是第一个导入，包含所有 Tailwind 的层和功能 */
@import 'tailwindcss';

/* @tailwind base;
  @tailwind components;
  @tailwind utilities; 
  通常 @import 'tailwindcss'; 会包含上述所有，
  如果 Tailwind v4 推荐分开，则按推荐来。
*/
/* 你的配置中只有 @tailwind utilities; 
   如果这是 v4 的推荐方式，请保持。但通常 @import 引入的是整个框架。
   如果遇到问题，尝试替换为：
   @tailwind base;
   @tailwind components;
   @tailwind utilities;
*/
@tailwind utilities;

:root {
	/* 定义你的应用颜色变量，提供回退值 (这些值在非 TMA 环境下生效) */
	/* 这些变量将在 +layout.svelte 中被 TMA 的 bindCssVars 覆盖 */
	--app-primary-color: 0 122 255; /* RGB for #007aff */
	--app-bg-color: 255 255 255; /* RGB for #ffffff */
	--app-text-color: 0 0 0; /* RGB for #000000 */
	--app-hint-color: 153 153 153; /* RGB for #999999 */
	--app-link-color: 0 122 255; /* RGB for #007aff */
	--app-button-color: 0 122 255; /* RGB for #007aff */
	--app-button-text-color: 255 255 255; /* RGB for #ffffff */
	--app-secondary-background-color: 242 242 247; /* RGB for #f2f2f7 */

	/* 以下是 TMA 其他可能提供的颜色，也建议提供回退值 */
	--app-header-background-color: 255 255 255;
	--app-section-background-color: 255 255 255;
	--app-section-separator-color: 224 224 224;
	--app-section-header-text-color: 102 102 102;
	--app-subtitle-text-color: 136 136 136;
	--app-destructive-text-color: 255 59 48; /* RGB for #ff3b30 */
}

/* 全局应用这些变量到根元素 */
html,
body {
	/* 将规则应用到 html 和 body，确保全页面覆盖 */
	background-color: rgb(var(--app-bg-color));
	color: rgb(var(--app-text-color));
	transition:
		background-color 0.2s ease,
		color 0.2s ease;

	/* 启用系统字体，提供更好的跨平台体验 */
	font-family:
		-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans',
		'Droid Sans', 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

	/* 确保 body 至少占满整个视口高度，以便布局正常工作 */
	min-height: 100vh;
	/* 使得 SvelteKit 的 %sveltekit.body% 内容能够填满视口 */
	height: 100vh;

	/* 防止用户选择文本，对于 Mini App 可能是个好主意 */
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* 优化滚动行为，使得滚动更平滑 */
	scroll-behavior: smooth;
	-webkit-overflow-scrolling: touch; /* iOS Safari 平滑滚动 */
}

a {
	color: rgb(var(--app-link-color));
}

/* 你仍然可以定义一些自定义类直接使用这些变量 */
.my-custom-element {
	background-color: rgb(var(--app-secondary-background-color));
}
