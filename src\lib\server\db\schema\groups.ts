// src/lib/server/db/schema/groups.ts
// CipherX TMA - 群组核心表定义 (最终重构版 V2.0)

import { pgTable, text, bigint, integer, timestamp, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { bots } from './bots';
import { groupTopics } from './group_topics';
import { groupStatusEnum, groupRoleEnum, groupMembershipStatusEnum } from './_enums';

/**
 * 群组主表 (V2.0)
 * 存储群组的核心、非语言相关的数据
 */
export const groups = pgTable('groups', {
    id: text('id').primaryKey(),
    slug: text('slug').unique().notNull(),
    telegramChatId: bigint('telegram_chat_id', { mode: 'number' }).unique().notNull(),

    // 结构化数据
    countryCode: text('country_code').notNull(),
    theme: text('theme').notNull(),

    // 管理信息
    creatorId: text('creator_id').notNull().references(() => users.id),
    botId: text('bot_id').notNull().references(() => bots.id),
    memberCount: integer('member_count').notNull().default(0),

    // 运营元数据
    status: groupStatusEnum('status').notNull().default('active'),
    announcementTopicId: bigint('announcement_topic_id', { mode: 'number' }),

    // 时间戳
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
});

/**
 * 群组翻译表 (新表)
 * 实现了可扩展的国际化
 */
export const groupTranslations = pgTable('group_translations', {
    groupId: text('group_id').notNull().references(() => groups.id, { onDelete: 'cascade' }),
    languageCode: text('language_code').notNull(), // 例如: 'en', 'zh-Hans', 'ja'
    name: text('name').notNull(),
    description: text('description'),
}, (table) => {
     return [
        primaryKey({ columns: [table.groupId, table.languageCode] })
    ];
});

/**
 * 群组成员关系表 (V2.0)
 * 集成了积分和状态系统
 */
export const groupMemberships = pgTable('group_memberships', {
    userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
    groupId: text('group_id').notNull().references(() => groups.id, { onDelete: 'cascade' }),
    role: groupRoleEnum('role').notNull().default('member'),
    
    // 积分与状态系统
    points: integer('points').notNull().default(100),
    status: groupMembershipStatusEnum('status').notNull().default('active'),
    
    // 订阅信息 (可为空)
    subscriptionExpiresAt: timestamp('subscription_expires_at', { withTimezone: true }),
    
    joinedAt: timestamp('joined_at', { withTimezone: true }).notNull().defaultNow()
}, (table) => {
      return [
        primaryKey({ columns: [table.userId, table.groupId] })
    ];
});


// --- 关系定义 ---

export const groupsRelations = relations(groups, ({ one, many }) => ({
    creator: one(users, { fields: [groups.creatorId], references: [users.id] }),
    managingBot: one(bots, { fields: [groups.botId], references: [bots.id] }),
    memberships: many(groupMemberships),
    translations: many(groupTranslations),
    topics: many(groupTopics)
}));

export const groupTranslationsRelations = relations(groupTranslations, ({ one }) => ({
    group: one(groups, { fields: [groupTranslations.groupId], references: [groups.id] })
}));

export const groupMembershipsRelations = relations(groupMemberships, ({ one }) => ({
    user: one(users, { fields: [groupMemberships.userId], references: [users.id] }),
    group: one(groups, { fields: [groupMemberships.groupId], references: [groups.id] })
}));