第一部分：MVP阶段目标国家与JSON范本

1. 目标国家选择 (V1.0)
   要选择“最有潜力的TG活跃用户大国”，我们通常会考虑几个因素：TG的普及率、用户付费能力、以及我们产品主题的文化接受度。基于这些，我建议我们的第一批MVP国家可以聚焦在以下几个：

英语核心区:

美国 (US): 市场巨大，用户多样性高。

英国 (GB): 欧洲的核心英语市场。

亚洲核心区:

日本 (JP): TG流行，文化上对细分社群接受度高。

台湾 (TW): 繁体中文市场的核心，社群活跃。

欧洲核心区:

德国 (DE): 欧洲最大的经济体之一，用户群体庞大。

MVP阶段，我们就先以这5个国家为目标，来准备配置文件。

2. JSON配置文件范本 (US.json)
   这份文件就是你接下来需要为上述每个国家创建的“蓝图”。我已经为你准备好了一个可以直接使用的美国版范本，并对欢迎消息做了优化。

请创建文件 config/countries/US.json 并使用以下内容：

JSON

{
"countryName": "United States",
"defaultLang": "en",
"standardTopics": [
{ "id": "announcements", "name": "📢 Announcements & Rules" },
{ "id": "general_chat", "name": "💬 General Chat" },
{ "id": "introductions", "name": "👋 Introductions" },
{ "id": "events_meetups", "name": "🎉 Events & Meetups" }
],
"provinces": [
{ "code": "AL", "name": "Alabama" },
{ "code": "AK", "name": "Alaska" },
{ "code": "AZ", "name": "Arizona" },
{ "code": "AR", "name": "Arkansas" },
{ "code": "CA", "name": "California" },
{ "code": "CO", "name": "Colorado" },
{ "code": "CT", "name": "Connecticut" },
{ "code": "DE", "name": "Delaware" },
{ "code": "FL", "name": "Florida" },
{ "code": "GA", "name": "Georgia" },
{ "code": "HI", "name": "Hawaii" },
{ "code": "ID", "name": "Idaho" },
{ "code": "IL", "name": "Illinois" },
{ "code": "IN", "name": "Indiana" },
{ "code": "IA", "name": "Iowa" },
{ "code": "KS", "name": "Kansas" },
{ "code": "KY", "name": "Kentucky" },
{ "code": "LA", "name": "Louisiana" },
{ "code": "ME", "name": "Maine" },
{ "code": "MD", "name": "Maryland" },
{ "code": "MA", "name": "Massachusetts" },
{ "code": "MI", "name": "Michigan" },
{ "code": "MN", "name": "Minnesota" },
{ "code": "MS", "name": "Mississippi" },
{ "code": "MO", "name": "Missouri" },
{ "code": "MT", "name": "Montana" },
{ "code": "NE", "name": "Nebraska" },
{ "code": "NV", "name": "Nevada" },
{ "code": "NH", "name": "New Hampshire" },
{ "code": "NJ", "name": "New Jersey" },
{ "code": "NM", "name": "New Mexico" },
{ "code": "NY", "name": "New York" },
{ "code": "NC", "name": "North Carolina" },
{ "code": "ND", "name": "North Dakota" },
{ "code": "OH", "name": "Ohio" },
{ "code": "OK", "name": "Oklahoma" },
{ "code": "OR", "name": "Oregon" },
{ "code": "PA", "name": "Pennsylvania" },
{ "code": "RI", "name": "Rhode Island" },
{ "code": "SC", "name": "South Carolina" },
{ "code": "SD", "name": "South Dakota" },
{ "code": "TN", "name": "Tennessee" },
{ "code": "TX", "name": "Texas" },
{ "code": "UT", "name": "Utah" },
{ "code": "VT", "name": "Vermont" },
{ "code": "VA", "name": "Virginia" },
{ "code": "WA", "name": "Washington" },
{ "code": "WV", "name": "West Virginia" },
{ "code": "WI", "name": "Wisconsin" },
{ "code": "WY", "name": "Wyoming" }
],
"welcomeMessage": "Welcome to the community\\! Please start by reading the rules in the _#📢Announcements & Rules_ topic\\. Feel free to introduce yourself in _#👋Introductions_\\. \n\nTo unlock the full experience and discover more, open our TMA app\\!"
}
第二部分：MVP阶段Bot开发需求文档
这份文档将是你接下来改造Bot模块的行动指南。

项目名称: CipherX TMA - Bot模块 MVP (V1.0)
目标: 实现一个能够自动化配置“细分社区群组”，并能有效引导用户进入TMA的核心Bot。

1. 配置管理 (Configuration)
   需求: Bot必须能够读取config/countries/目录下的JSON文件，以获取群组初始化的所有必要数据。

验收标准:

[ ] Bot的/initialize_group指令能够根据传入的country_code参数，正确加载对应的JSON文件。

[ ] 如果找不到对应的JSON文件，Bot应向管理员回复明确的错误信息。

2. 群组初始化功能 (/initialize_group指令)
   需求: 这是面向开发者/管理员的核心功能，必须健壮、可靠。

验收标准:

[ ] Bot能正确解析指令中的所有参数（slug, country_code, theme）。

[ ] Bot能完成“双重权限验证”（用户权限和Bot自身在群组内的权限）。

[ ] Bot能根据加载的JSON配置，自动在群组内完成以下所有操作：

[ ] 创建所有standardTopics中定义的话题。

[ ] 遍历provinces列表，为每一个州/省创建对应的格式化话题（例如 #CA｜California）。

[ ] 在“公告”话题中，发布welcomeMessage的内容，并将其置顶。

[ ] （可选，建议实现）根据theme参数，设置一个预设的群头像。

[ ] 在初始化流程的关键步骤（开始、创建话题、完成），向群组内发送进度反馈消息。

[ ] 初始化成功后，将群组的核心信息（slug, telegramChatId等）存入数据库groups表。

3. 用户引导功能 (User Onboarding)
   需求: Bot需要在新成员加入时，主动引导其使用TMA。这是MVP阶段最重要的用户转化功能。

验收标准:

[ ] Bot能够监听到新成员加入群组的事件 (bot.on('chat_member'))。

[ ] 当有新成员加入时，Bot向该群组发送一条欢迎消息。

[ ] 该欢迎消息必须包含一个内联键盘 (Inline Keyboard)，键盘上至少有一个醒目的按钮，文字为“🚀 Open App”或“启动应用”。

[ ] 点击此按钮，能够直接拉起你的Telegram Mini App。

4. 基础指令 (Basic Commands)
   需求: 为Bot提供最基础的帮助和引导。

验收标准:

[ ] 实现/start指令，回复Bot的欢迎和简介信息。

[ ] 实现/help指令，简要说明Bot的功能，特别是为管理员说明/initialize_group指令的用法。

[ ] 所有回复文本，都必须针对MarkdownV2格式进行特殊字符转义，确保不会报错。

[ ] 通过 bot.api.setMyCommands(...)，将/start和/help设置为用户可见的命令菜单。

MVP阶段Bot开发需求文档 (V1.1 - 国际化增强版)
项目名称: CipherX TMA - Bot模块 MVP (V1.0)
目标: 实现一个能够本地化地自动化配置“细分社区群组”，并能有效引导用户进入TMA的核心Bot。

1. 国际化 (i18n) 与本地化 (L10n) 核心需求
   需求: Bot在进行群组初始化时，必须使用对应国家config/\*.json文件中defaultLang字段指定的语言，来生成所有面向用户的文本内容。

验收标准:

[ ] 当初始化一个country_code=JP的群组时，所有创建的话题名称、发布的欢迎消息，都必须是日语。

[ ] 当初始化一个country_code=DE的群组时，所有内容都必须是德语。

2. 配置管理 (Configuration)
   需求: Bot必须能够读取config/countries/目录下的JSON文件，以获取群组初始化的所有必要数据。

验收标准:

[ ] 为所有MVP国家（US, GB, JP, TW, DE）创建对应的JSON文件。

[ ] (本地化要求) JP.json, TW.json, DE.json文件中的countryName, standardTopics.name, provinces.name, 和 welcomeMessage 字段，必须使用对应国家/地区的母语填写。

[ ] Bot的/initialize_group指令能够根据传入的country_code参数，正确加载对应的本地化JSON文件。

JSON范例 (JP.json):

JSON

{
"countryName": "日本",
"defaultLang": "ja",
"standardTopics": [
{ "id": "announcements", "name": "📢 お知らせ＆ルール" },
{ "id": "general_chat", "name": "💬 総合チャット" },
{ "id": "introductions", "name": "👋 自己紹介" }
],
"provinces": [
{ "code": "JP-13", "name": "東京都" },
{ "code": "JP-27", "name": "大阪府" },
// ... 包含所有都道府县
],
"welcomeMessage": "コミュニティへようこそ\\! まずは「#📢お知らせ＆ルール」のトピックをお読みください。自己紹介は「#👋自己紹介」でどうぞ。TMAアプリを開いて、もっと多くの機能をお楽しみください\\!"
} 3. 群组初始化功能 (/initialize_group指令)
需求: 这是面向开发者/管理员的核心功能，必须健壮、可靠，并支持本地化。

验收标准:

... (其他验收标准不变) ...

[ ] (本地化要求) Bot创建的所有话题名称，必须直接使用从本地化JSON文件中读取的name字段。例如，为日本创建地区话题时，话题名应为#東京都，而不是#Tokyo。

[ ] (本地化要求) Bot发布的置顶欢迎消息，必须是从本地化JSON文件中读取的welcomeMessage内容。

4. 用户引导功能 (User Onboarding)
   需求: Bot需要在新成员加入时，主动引导其使用TMA。

验收标准:

... (其他验收标准不变) ...

[ ] (本地化要求) Bot发送的欢迎消息（包含“启动应用”按钮的），其文本内容也应考虑本地化。可以在Bot代码中根据群组的country_code做一个简单的语言判断。

5. 基础指令 (Basic Commands)
   需求: 为Bot提供最基础的帮助和引导。

验收标准:

... (其他验收标准不变) ...

[ ] Bot的/start和/help指令的回复内容，应优先使用英语作为通用语言，因为这些指令可能在任何地方被用户私聊触发。
