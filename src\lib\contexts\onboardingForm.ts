import { getContext, setContext } from 'svelte';
import type { SuperForm } from 'sveltekit-superforms';
import type { z } from 'zod';
import type { onboardingFormSchema } from '$lib/schemas/profile.schema';

// 1. 定义我们 Context 的类型
// 它就是 superForm 返回的所有东西
export type OnboardingFormContext = SuperForm<z.infer<typeof onboardingFormSchema>>;

// 2. 创建一个唯一的 Key，用于设置和获取 Context
const CONTEXT_KEY = Symbol('onboarding_form_context');

// 3. 封装成两个方便的函数
export function setOnboardingFormContext(context: OnboardingFormContext) {
	setContext(CONTEXT_KEY, context);
}

export function getOnboardingFormContext(): OnboardingFormContext {
	return getContext<OnboardingFormContext>(CONTEXT_KEY);
}
