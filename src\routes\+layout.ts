import { browser } from '$app/environment';
import { authSessionStore, displayUserProfileStore, tgSdkInitializedStore } from '$stores';
import { get } from 'svelte/store';
import { devLog, tmaInit } from '$utils';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ data }) => {
	if (!get(displayUserProfileStore)) displayUserProfileStore.set(data.user);
	if (!get(authSessionStore)) authSessionStore.set(data.session);
	// ✅ 确保 tmaInit 只在客户端运行，且只运行一次
	if (browser) {
		const tgSdkInitialized = get(tgSdkInitializedStore);
		// 检查我们的“信号旗” store
		if (!tgSdkInitialized) {
			try {
				tmaInit();
			} catch (e) {
				console.error('TMA SDK Init Failed in layout.ts:', e);
			}
		}
	}

	return data;
};
