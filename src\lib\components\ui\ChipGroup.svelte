<script lang="ts">
	import Chip from './Chip.svelte';

	type Item = {
		value: any;
		label: string;
	};

	type Props = {
		items: Item[];
		value?: any | any[];
		multiple?: boolean;
		// ✅ 1. 明确定义 onChange 回调函数属性
		onChange: (newValue: any | any[]) => void;
	};

	// ✅ 2. 移除 $bindable，接收 onChange prop
	let { items, value, multiple = false, onChange }: Props = $props();

	function handleClick(itemValue: any) {
		let newValue: any | any[];

		if (multiple) {
			const currentValue = Array.isArray(value) ? [...value] : [];
			const index = currentValue.indexOf(itemValue);
			if (index > -1) {
				currentValue.splice(index, 1);
			} else {
				currentValue.push(itemValue);
			}
			newValue = currentValue;
		} else {
			newValue = value === itemValue ? null : itemValue;
		}

		// ✅ 3. 只调用 onChange 将新值“报告”给父组件
		onChange(newValue);
	}

	function isSelected(itemValue: any): boolean {
		if (multiple) {
			return Array.isArray(value) && value.includes(itemValue);
		}
		return value === itemValue;
	}
</script>

<div class="flex flex-wrap gap-2">
	{#each items as item (item.value)}
		<Chip
			label={item.label}
			value={item.value}
			selected={isSelected(item.value)}
			onclick={() => handleClick(item.value)}
		/>
	{/each}
</div>
