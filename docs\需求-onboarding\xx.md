首先需求是移动端特别友善的引导流程,分为主引导页和向左滑动的副引导页
技术栈为 UI: @telegram-apps/sdk-svelte 里有telegram重要的原生的UI组件和功能 + konstaUI + tailwindcss
UX为 用户首先进入引导页以后 已有数据库读取出来
export const dbUserSchema = z.object({
// 核心标识
id: createIdSchema(ID_PREFIX_USER),
telegramUserId: telegramUserIdSchema,
tonWalletAddress: tonWalletAddressSchema.nullable(),
inviterId: createIdSchema(ID_PREFIX_USER).nullable(),
kinkMapCode: kinkMapCodeSchema,

    // 基础资料
    nickname: nicknameSchema,
    telegramUsername: telegramUsernameSchema.nullable(),
    age: ageSchema.nullable(),
    heightCm: heightSchema.nullable(),
    weightKg: weightSchema.nullable(),
    countryCode: countryCodeSchema.nullable(),
    provinceCode: provinceCodeSchema.nullable(),
    city: citySchema.nullable(),
    languageCode: languageCodeSchema.nullable(),

    // 高级资料
    bio: bioSchema.nullable(),
    profileImageUrl: urlSchema.nullable(),
    orientation: z.enum(ORIENTATION_VALUES).nullable(),
    bodyType: z.enum(BODY_TYPE_VALUES).nullable(),
    presentationStyle: z.enum(PRESENTATION_STYLE_VALUES).nullable(),
    relationshipStatus: z.enum(RELATIONSHIP_STATUS_VALUES).nullable(),

    // Kink 相关数据
    kinkCategoryBitmask: z.bigint().optional().nullable(),
    kinkRatings: z.record(z.string(), kinkRatingsValueSchema).optional().nullable(),

    // 系统计算字段
    profileCompletenessScore: z.number().int(),
    trustScore: z.number().int(),
    vipLevel: z.number().int(),
    pointBalance: z.number().int(),

    // 状态字段
    isActive: z.boolean(),
    isBanned: z.boolean(),

    // 时间戳
    createdAt: z.date(),
    updatedAt: z.date(),
    lastActiveAt: z.date()

});

这些有用的字段 然后 后续用户的UIUX原则为,尽量避免激活用户的键盘输入体验,而尽量使用slide 或者 chip 让用户填选资料,有默认的资料可以替用户先填上,然后特殊的 country province city的填写 可以有 对应的 country-province-city 包可以使用,我希望是用户首先是通过languagecode 给他一个默认的country 选择,也可以通过输入+1 或者 +86 这样的填写数字的方式是,快速的获得对应的国家的联级选框类似的,而对于副引导页,对于 kinkCategoryBitmask 提醒用户这是高级资料,我们注重绝对的隐私和安全,这只是为了后续的高级搜索功能,能精准匹配所用,也为了制作自己独特的kinkmap 分享页的数据使用, 而一个折叠的选择框下面 则是 kinkratings,设计到的各种chip和对应的积分,同时这里需要提醒用户一些SSC等BDSM相关的安全提示和免责声明, 然后这个副引导页最好可以复用,因为后续我们的营销分享拉新页的数据填选主要来自于这里
uiux中的一些界面文字请使用paraglide进行管理
