import type { KinkRatingValue } from '$lib/constants/kinks';
import { m } from '../../paraglide/messages';

export function getRatingLabel(rating: KinkRatingValue): string {
	const labels: Record<KinkRatingValue, string> = {
		'-2': m.data_kink_rating_label_neg2(),
		'-1': m.data_kink_rating_label_neg1(),
		'0': m.data_kink_rating_label_0(),
		'1': m.data_kink_rating_label_1(),
		'2': m.data_kink_rating_label_2(),
		'3': m.data_kink_rating_label_3(),
		'4': m.data_kink_rating_label_4(),
		'5': m.data_kink_rating_label_5()
	};
	return labels[rating] || labels['0'];
}

// export function getRatingColor(rating: KinkRatingValue): string {
// 	if (rating <= -1) return 'text-red-600';
// 	if (rating === 0) return 'text-gray-500';
// 	if (rating <= 2) return 'text-yellow-600';
// 	if (rating <= 4) return 'text-green-600';
// 	return 'text-purple-600';
// }

export function getRatingBadgeColor(rating: KinkRatingValue): string {
	if (rating <= -1) return 'bg-red-100 text-red-800';
	if (rating === 0) return 'bg-gray-100 text-gray-800';
	if (rating <= 2) return 'bg-yellow-100 text-yellow-800';
	if (rating <= 4) return 'bg-green-100 text-green-800';
	return 'bg-purple-100 text-purple-800';
}
