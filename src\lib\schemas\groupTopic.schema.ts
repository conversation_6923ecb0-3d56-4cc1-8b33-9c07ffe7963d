// src/lib/schemas/groupTopic.schema.ts
// CipherX TMA - 群组话题相关验证规则

import { z } from 'zod';
import { createIdSchema } from '$lib/schemas/common.schema.js';
import { topicStatusEnum, topicTypeEnum } from '$lib/server/db/schema/_enums';
import { ID_PREFIX_USER, ID_PREFIX_GROUP } from '$lib/constants/prefixes.js';

/**
 * 创建新话题时的验证 Schema
 * 用于未来在TMA中手动创建话题的表单
 */
export const createTopicSchema = z.object({
	groupId: createIdSchema(ID_PREFIX_GROUP),
	telegramTopicId: z.bigint(),

	name: z
		.string()
		.trim()
		.min(1, 'lib_validation_topic_name_required')
		.max(100, 'lib_validation_topic_name_too_long'),

	type: z.enum(topicTypeEnum.enumValues).default('custom'),
	creatorId: createIdSchema(ID_PREFIX_USER)
});

/**
 * 更新话题信息时的验证 Schema
 */
export const updateTopicSchema = z.object({
	topicId: createIdSchema('tpc_'), // 假设话题ID前缀为 'tpc_'

	// 只允许更新部分字段
	name: z
		.string()
		.trim()
		.min(1, 'lib_validation_topic_name_required')
		.max(100, 'lib_validation_topic_name_too_long')
		.optional(),

	status: z.enum(topicStatusEnum.enumValues).optional()
});

/**
 * 话题搜索过滤器的验证 Schema
 */
export const topicSearchFiltersSchema = z.object({
	groupId: createIdSchema(ID_PREFIX_GROUP),
	type: z.enum(topicTypeEnum.enumValues).optional(),
	status: z.enum(topicStatusEnum.enumValues).optional(),
	searchQuery: z.string().optional(),
	sortBy: z.enum(['lastActivityAt', 'messageCount', 'createdAt']).default('lastActivityAt')
});

// --- 导出Zod推断的TypeScript类型 ---

export type CreateTopicData = z.infer<typeof createTopicSchema>;
export type UpdateTopicData = z.infer<typeof updateTopicSchema>;
export type TopicSearchFilters = z.infer<typeof topicSearchFiltersSchema>;
