// src/lib/server/db/schema/auth.ts
// CipherX TMA - Lucia 认证相关表定义 (包含关系)

import { pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';

export const sessions = pgTable('sessions', {
	id: text('id').primaryKey(),
	// 关键修正点：添加外键约束
	userId: text('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	expiresAt: timestamp('expires_at', { withTimezone: true, mode: 'date' }).notNull()
});

export const keys = pgTable('keys', {
	id: text('id').primaryKey(),
	// 关键修正点：添加外键约束
	userId: text('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	hashedPassword: text('hashed_password')
});

// 关键修正点：将相关的 relations 定义移到这里
export const sessionsRelations = relations(sessions, ({ one }) => ({
	user: one(users, {
		fields: [sessions.userId],
		references: [users.id]
	})
}));

export const keysRelations = relations(keys, ({ one }) => ({
	user: one(users, {
		fields: [keys.userId],
		references: [users.id]
	})
}));

// 类型导出
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
