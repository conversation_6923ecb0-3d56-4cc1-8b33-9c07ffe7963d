import { writable } from 'svelte/store';
import type { Platform, User as SDKUser, Version } from '@telegram-apps/sdk-svelte';
// 用于存储 Telegram 用户信息
// 明确表示 store 的值可以是 User 类型，也可以是 undefined
export const tgUserStore = writable<SDKUser | undefined | null>(undefined);

// 用于存储 Telegram Mini App 的平台信息
export const tgPlatformStore = writable<Platform | undefined | null>(undefined);

// 用于存储 Telegram Mini App 的版本信息
export const tgVersionStore = writable<Version | undefined | null>(undefined);

export const tgSdkInitializedStore = writable<boolean>(false);
