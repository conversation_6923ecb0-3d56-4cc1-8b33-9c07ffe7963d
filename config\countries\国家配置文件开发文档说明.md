国家配置文件 (config/countries/xx.json) 规范与施工蓝图 (V1.0)

1. 文档目标 (Document Objective)
   本文件旨在定义 CipherX TMA 项目中，所有国家配置文件 (xx.json) 的标准数据结构和内容填写规范。所有Bot的群组自动化初始化行为，都将严格基于此文件结构。团队在未来扩展支持新国家时，必须遵循本规范创建对应的配置文件。

2. 文件位置与命名 (File Location & Naming)
   位置: 所有国家配置文件都必须存放在项目根目录下的 config/countries/ 文件夹中。

命名: 文件名必须使用对应国家的 ISO 3166-1 alpha-2 标准代码（大写）。

示例: 美国 -> US.json, 日本 -> JP.json, 德国 -> DE.json

3. 数据结构详解 (Detailed Data Structure)
   每个国家的JSON文件都必须包含以下7个顶级字段：

字段名 类型 是否必需 说明与填写指南
countryName string ✅ 是 [母语] 国家/地区的官方全名。例如，在JP.json中应填写“日本”。
defaultLang string ✅ 是 该国家/地区最主要的语言代码 (ISO 639-1)。例如 "en", "ja", "de", "zh-Hans"。
description string ✅ 是 [母语] 将被Bot设置为群组描述的文本。应简洁地介绍该国社群的用途。
welcomeMessage string ✅ 是 [母语] 将被Bot发布并置顶在“公告”话题中的欢迎长文。【重要】 此文本必须遵循 MarkdownV2 格式，所有特殊字符（., !, -, (, )等）都必须用反斜杠\进行转义。
standardTopics Array<Object> ✅ 是 群组的通用标准话题列表。用于创建社区的基础框架。具体结构见下表。
provinces Array<Object> ✅ 是 该国家/地区的所有主要行政区划（州、省、县等）列表。用于批量创建地区话题。具体结构见下表。
provinceTopicDefaults Object ✅ 是 为所有provinces话题设置一个统一的、默认的视觉样式。具体结构见下表。

Export to Sheets
standardTopics 对象结构:

字段名 类型 必需 说明
id string ✅ [英文] 话题的内部唯一标识符，必须在所有国家的JSON文件中保持一致。例如 announcements。
name string ✅ [母语] 话题的显示名称。
emoji string ✅ [Emoji] 拼接在话题名称前的Emoji表情。
iconColor number ✅ 话题图标的颜色代码。【重要】 必须是Telegram API指定的6个特定整数值之一，详见附录A。

Export to Sheets
provinces 对象结构:

字段名 类型 必需 说明
code string ✅ [英文/官方缩写] 行政区划的缩写。例如 "CA", "JP-13"。
name string ✅ [母语] 行政区划的全名。例如 "California", "東京都"。

Export to Sheets
provinceTopicDefaults 对象结构:

字段名 类型 必需 说明
emoji string ✅ [Emoji] 所有省份话题统一使用的Emoji表情。
iconColor number ✅ [颜色代码] 所有省份话题统一使用的图标颜色。同样必须是附录A中指定的6个值之一。

Export to Sheets 4. 完整范本 (US.json)
这是一个可以直接复制和修改的、符合上述所有规范的美国配置文件范本。

JSON

{
"countryName": "United States",
"defaultLang": "en",
"description": "Welcome to the official US community for CipherX! All topics and discussions are held here.",
"welcomeMessage": "Welcome to the community\\! Please start by reading the rules in the _📢 Announcements & Rules_ topic\\. Feel free to introduce yourself in the _👋 Introductions_ topic\\. \n\nTo unlock the full experience and discover more, open our TMA app\\!",
"standardTopics": [
{
"id": "announcements",
"name": "Announcements & Rules",
"emoji": "📢",
"iconColor": xxx
},
{
"id": "general_chat",
"name": "General Chat",
"emoji": "💬",
"iconColor": xxx
},
{
"id": "introductions",
"name": "Introductions",
"emoji": "👋",
"iconColor": xxx
},
{
"id": "events_meetups",
"name": "Events & Meetups",
"emoji": "🎉",
"iconColor": xxx
}
],
"provinces": [
{ "code": "AL", "name": "Alabama" },
{ "code": "AK", "name": "Alaska" },
{ "code": "AZ", "name": "Arizona" },
{ "code": "AR", "name": "Arkansas" },
{ "code": "CA", "name": "California" }
],
"provinceTopicDefaults": {
"emoji": "🗺️",
"iconColor": xxx
}
} 5. 操作流程与最佳实践
数据来源: provinces列表的准确数据，建议通过country-state-cityNPM包脚本化生成，或从维基百科等权威来源查找。

团队协作:

创建新的国家配置文件时，应新建一个Git分支（如 feature/add-country-FR）。

完成文件编写后，提交Pull Request，供其他团队成员进行审查(Review)。

JSON验证: 在提交代码前，务必使用VS Code的Linter或在线JSON验证工具，检查文件格式是否完全正确。

MarkdownV2转义: 再次强调，welcomeMessage中所有特殊字符都必须用\转义，否则Bot发送时会报错。

附录A：iconColor 官方颜色代码参考
iconColor字段不接受任意的RGB整数值，而必须是以下 6个预设值 之一：

颜色 (Color) 整数值 (Integer Value)
🟢 绿色 (Green) 7322096
🟡 黄色 (Yellow) 16766590
💜 紫色 (Violet) 13338331
💙 蓝色 (Blue) 9367192
🩷 粉色 (Pink) 16749490
❤️ 红色 (Red) 16478047
