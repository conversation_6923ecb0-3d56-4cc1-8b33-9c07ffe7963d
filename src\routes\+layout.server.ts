import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';
import { getDisplayUserProfile } from '$lib/server/services/user.service';
import { devLog, ONBOARDING_THRESHOLD, superJson } from '$lib/utils';

export const load: LayoutServerLoad = async ({ locals, url }) => {
	// 从 hooks.server.ts 获取基础的认证信息
	const { user, session } = locals;

	// ✅ 1. 检查用户是否已通过基础认证
	// 如果用户未登录，直接返回 null，让前端 UI 显示未登录状态
	if (!user || !session) {
		return {
			session: null,
			user: null
		};
	}

	// ✅ 2. 如果用户已登录，则执行核心逻辑
	try {
		// 从数据库获取最新的、完整的用户资料
		const displayUserProfile = await getDisplayUserProfile(user.id);
		// 这是一个安全检查，以防 session 有效但数据库中没有对应用户
		if (!displayUserProfile) {
			console.error(
				`[Root Layout] Critical error: User ${user.id} found in session, but not in DB.`
			);
			return { session: null, user: null };
		}

		// // ✅ 3. 在这里执行全局的“Onboarding 守卫”逻辑
		// const needsOnboarding = displayUserProfile?.profileCompletenessScore < ONBOARDING_THRESHOLD;

		// // 如果用户需要 Onboarding，并且他们当前访问的不是 Onboarding 页面自身
		// // (这个 `url.pathname` 检查是为了防止无限重定向循环)
		// if (needsOnboarding && url.pathname !== '/onboarding' && url.pathname !== '/') {
		// 	throw redirect(303, '/');
		// }

		// ✅ 4. 如果一切正常，无需重定向，则返回经过安全序列化的完整用户数据
		//    这份数据将成为所有子页面都能访问的、权威的初始数据
		return {
			session,
			user: displayUserProfile
		};
	} catch (e) {
		console.error('[Root Layout] An error occurred while fetching user profile or redirecting:', e);
		// 如果在获取或重定向过程中发生任何错误，返回一个清晰的错误状态
		return {
			session,
			user: null
		};
	}
};
