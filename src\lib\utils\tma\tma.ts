import { init, retrieveLaunchParams } from '@telegram-apps/sdk-svelte';
import { tgPlatformStore, tgSdkInitializedStore, tgUserStore, tgVersionStore } from '$lib/stores';

export function tmaInit() {
	init(); // 再次调用 init() 是安全的，SDK 内部会处理重复初始化
	const { tgWebAppData, tgWebAppPlatform, tgWebAppVersion } = retrieveLaunchParams();
	tgUserStore.set(tgWebAppData.user);
	tgPlatformStore.set(tgWebAppPlatform);
	tgVersionStore.set(tgWebAppVersion);
	tgSdkInitializedStore.set(true);
	console.log('✅ TMA SDK re-initialized and stores populated after server session.');
}
