<script lang="ts">
    import { Page, Navbar, Block, Segmented } from 'konsta/svelte';
    import { superForm, setMessage, type FormResult } from 'sveltekit-superforms';
    import { zodClient } from 'sveltekit-superforms/adapters';
    import { setOnboardingFormContext } from '$lib/contexts/onboardingForm';
    import OnboardingPageOne from './OnboardingPageOne.svelte';
    import OnboardingPageTwo from './OnboardingPageTwo.svelte';
    import { onboardingFormSchema, type DisplayUserProfile } from '$lib/schemas/profile.schema';
    import type { PageData, ActionData } from './$types';
    import * as m from '$lib/paraglide/messages';
    import { goto } from '$app/navigation';
    import { displayUserProfileStore } from '$stores';
    import { parse } from 'devalue';
    import { onMount } from 'svelte'; // onDestroy 在这里不是必需的，除非你有自定义的清理逻辑
	import ProfileCompletenessIndicator from '$components/ui/ProfileCompletenessIndicator.svelte';
	import Loader from '$components/feature/Loader.svelte';

    let { data }: { data: PageData } = $props();

    const replacer = (key, value) => {
        if (typeof value === 'bigint') {
            return value.toString();
        }
        return value;
    };

    // ✅ 将这些变量声明为 $state()，以确保响应式
    let formContext = $state(null);
    let formStore = $state(null);
    let submitting = $state(false);
    let enhance = $state(null);

    // 页面切换的状态（已经是 $state 了）
    let currentPage = $state(0);

    onMount(() => {
        // 初始化 superForm
        const sForm = superForm(data.form, {
            id: 'onboarding_form', // 添加一个唯一的ID，以避免重复警告（如果适用）
            SPA: true,
            validators: zodClient(onboardingFormSchema),
            dataType: 'json',
            onUpdate: async ({ form: formProxy, cancel }) => {
                if (!formProxy.valid) {
                    setMessage(formProxy, '请检查表单中的错误。', { status: 422 });
                    cancel();
                    return;
                }

                const response = await fetch('/api/onboarding/submit', {
                    method: 'POST',
                    body: JSON.stringify(formProxy.data, replacer),
                    headers: { 'Content-Type': 'application/json' }
                });

                const resultText = await response.text();
                let parsedResultData;
                try {
                    parsedResultData = parse(resultText);
                } catch (e) {
                    console.error("DEBUG: Failed to parse API response in onUpdate:", e, resultText);
                    setMessage(formProxy, '服务器响应解析失败。', { status: 500 });
                    cancel();
                    return;
                }

                if (response.ok) {
                    console.log("DEBUG: Manual fetch successful. Updating displayUserProfileStore and attempting goto.");
                    $displayUserProfileStore = parsedResultData.userProfile;
                    await goto('/profile');
                    console.log("DEBUG: goto('/profile') initiated after manual fetch.");
                } else {
                    const errorMessage = parsedResultData.form?.message || parsedResultData.message || '发生未知错误。';
                    setMessage(formProxy, errorMessage, { status: response.status });
                    cancel();
                }
            }
        });

        // ✅ 将 superForm 的结果赋值给 $state() 变量
        formContext = sForm;
        formStore = sForm.form; // 正确获取 form store
        submitting = sForm.submitting; // 获取 submitting store
        enhance = sForm.enhance; // 获取 enhance 函数

        setOnboardingFormContext(formContext);
    });
</script>

<Page>
    {#if formContext && formStore && enhance}
        <form method="POST" use:enhance={enhance}> 
            <Navbar title={m.routes_onboarding_title()} />

            <ProfileCompletenessIndicator profileData={$formStore} />

            <Block class="!py-2">
                <Segmented>
                    <button
                        type="button"
                        class="k-button..."
                        disabled={$submitting}
                        onclick={() => (currentPage = 0)}
                    >
                        {m.routes_onboarding_tab_basic()}
                    </button>
                    <button
                        type="button"
                        class="k-button..."
                        disabled={$submitting}
                        onclick={() => (currentPage = 1)}
                    >
                        {m.routes_onboarding_tab_advanced()}
                    </button>
                </Segmented>
            </Block>

            {#if currentPage === 0}
                <OnboardingPageOne onSwitchPage={() => (currentPage = 1)} />
            {/if}
            {#if currentPage === 1}
                <OnboardingPageTwo onSwitchPage={() => (currentPage = 0)} />
            {/if}
        </form>
    {:else}
        <Loader />
    {/if}
</Page>