开发指南：Bot大脑 V1 - 群组自动化初始化模块
文档目标 (Objective):
本指南的目标是完成Bot的核心自动化功能开发。最终成果是，当管理员在Telegram群组中执行一个特定指令时，Bot能够自动完成该群组的全部标准化配置，并将其信息记录到数据库中。

前置条件 (Prerequisites):

SvelteKit项目已搭建完成。

Drizzle ORM已配置，并连接到PostgreSQL数据库。groups和bots表的schema已根据我们之前的讨论定义好。

grammy库已安装在项目中。

你已拥有至少一个用于测试的Telegram Bot Token。

核心组件 (Core Components):
我们将构建三个核心组件：

配置中心: 一系列JSON文件，作为Bot的“知识库”。

Webhook入口: 一个SvelteKit的API路由，作为Bot的“耳朵”，接收来自Telegram的消息。

指令处理器: Bot的核心逻辑，作为Bot的“大脑”，处理初始化指令。

开发步骤 (Development Steps)
步骤 1：构建配置中心 (Configuration Center)
目标: 创建Bot执行任务时所需的数据源。

任务 1.1: 创建目录结构
在你的项目根目录下，创建以下结构：

/config
├── /countries
│ ├── US.json
│ └── CN.json
└── /themes
└── (暂不创建文件，但保留目录)
任务 1.2: 定义并创建初始配置文件
创建 US.json 和 CN.json 文件。它们是Bot初始化美国和中国群组的“说明书”。将以下内容作为V1版本的最终范本复制进去。

config/countries/US.json:

JSON

{
"countryName": "United States",
"defaultLang": "en",
"standardTopics": [
{ "id": "announcements", "name": "📢 Announcements & Rules" },
{ "id": "general_chat", "name": "💬 General Chat" },
{ "id": "introductions", "name": "👋 Introductions" }
],
"provinces": [
{ "code": "CA", "name": "California" },
{ "code": "TX", "name": "Texas" },
{ "code": "FL", "name": "Florida" },
{ "code": "NY", "name": "New York" }
],
"welcomeMessage": "Welcome to the community! Please read the rules in #Announcements and introduce yourself in #Introductions."
}
config/countries/CN.json:

JSON

{
"countryName": "中国",
"defaultLang": "zh",
"standardTopics": [
{ "id": "announcements", "name": "📢 公告与规则" },
{ "id": "general_chat", "name": "💬 综合闲聊" },
{ "id": "introductions", "name": "👋 新人报到" }
],
"provinces": [
{ "code": "GD", "name": "广东省" },
{ "code": "JS", "name": "江苏省" },
{ "code": "SD", "name": "山东省" },
{ "code": "ZJ", "name": "浙江省" }
],
"welcomeMessage": "欢迎加入社区！请先阅读 #公告与规则 的内容，也欢迎在 #新人报到 中介绍自己。"
}
步骤 2：实现 Webhook 入口
目标: 让SvelteKit应用能接收并处理Telegram发来的消息。

文件路径: src/routes/api/webhook/[tokenAlias]/+server.ts

任务 2.1: 别名与真实Token的映射逻辑

安全说明: [tokenAlias] 是我们在URL中使用的安全别名（如 usa-main-bot），而不是完整的Bot Token。

环境变量: 将你真实的Bot Token存储在 .env 文件中，例如：BOT_TOKEN_USA="123:ABC..."。

逻辑实现: 在 +server.ts 中，编写一个函数。该函数接收URL中的tokenAlias参数，然后根据这个别名去环境变量（通过SvelteKit的 $env/dynamic/private 导入）中查找并返回对应的真实Token。建议使用一个Map来缓存已创建的Bot实例以提升性能。

任务 2.2: 绑定 grammY

在 +server.ts 的 POST 请求处理函数中，调用上述函数获取Bot实例。

使用 webhookCallback(bot, 'sveltekit') 将请求无缝交给grammY处理。

步骤 3：实现 /initialize_group 指令处理器
目标: 构建Bot大脑的核心逻辑。建议将此逻辑封装在 src/lib/server/bot/commands/initializeGroup.ts 中，以保持代码整洁。

任务 3.1: 指令注册与参数解析

在你的主Bot实例文件中，注册指令：bot.command('initialize_group', initializeGroupHandler);

创建 initializeGroupHandler 异步函数。它的首要任务是解析 ctx.match 中的参数字符串 (slug=... country_code=...)。你可以用正则表达式或简单的字符串分割来完成。最终目标是得到一个清晰的参数对象，例如 { slug: 'cipherx-us-msm', country_code: 'US', ... }。

必须做错误处理: 如果缺少必要参数，立刻 ctx.reply() 提示管理员并 return。

任务 3.2: 权限验证
在 initializeGroupHandler 中，按顺序执行以下验证：

验证用户权限: 检查 ctx.from.id 是否在你的管理员ID列表中（建议将管理员ID列表也存储在环境变量中）。

验证Bot权限: 调用 ctx.getChatMember(ctx.me.id) 获取Bot自身状态，检查 status 是否是 administrator，以及 can_manage_topics, can_set_chat_photo 等关键权限是否为 true。

任何验证失败，都应 ctx.reply() 清晰地告知管理员原因并 return。

任务 3.3: 加载配置并执行SOP
这是核心的自动化流程。

TypeScript

// Conceptual code inside initializeGroupHandler, after parsing and validation

try {
// 1. 加载配置
const { country_code, theme } = params;
const countryConfig = (await import(`../../../../config/countries/${country_code}.json`, { with: { type: 'json' } })).default;
// const themeConfig = ... (未来)

    await ctx.reply('🚀 Initialization started. Please wait...');

    // 2. 设置品牌
    // await ctx.api.setChatPhoto(...); // 暂时可以跳过，先实现核心逻辑
    await ctx.api.setChatDescription(ctx.chat.id, "Welcome! This group is currently under automated setup.");

    // 3. 创建标准话题并记录公告话题ID
    let announcementTopicId;
    for (const topic of countryConfig.standardTopics) {
        const createdTopic = await ctx.api.createForumTopic(ctx.chat.id, topic.name);
        if (topic.id === 'announcements') {
            announcementTopicId = createdTopic.message_thread_id;
        }
    }

    // 4. 批量创建地区话题
    for (const province of countryConfig.provinces) {
        const topicName = `${province.code}｜${province.name}`;
        await ctx.api.createForumTopic(ctx.chat.id, topicName);
    }

    // 5. 发布并置顶欢迎语
    if (announcementTopicId) {
        const welcomeMsg = await ctx.api.sendMessage(ctx.chat.id, countryConfig.welcomeMessage, { message_thread_id: announcementTopicId });
        await ctx.api.pinChatMessage(ctx.chat.id, welcomeMsg.message_id);
    }

    // ... 其他权限、指令设置

    // 6. 成功反馈
    await ctx.reply('✅ Initialization complete! The community is ready.');

    // 7. 数据持久化（下一步）

} catch (error) {
console.error("Initialization failed:", error);
await ctx.reply(`❌ An error occurred during initialization: ${error.message}`);
}
任务 3.4: 数据库持久化

在 try 模块的最后，成功反馈给管理员之后，执行数据库操作。

使用你的Drizzle实例 (db)，将从指令参数和ctx中获取到的信息（slug, ctx.chat.id, country_code, theme, announcementTopicId 等）插入到 groups 表中。

测试与验证 (Testing & Validation)
本地环境: 启动SvelteKit开发服务器 (npm run dev)。

公网暴露: 使用 ngrok 等工具，为你本地的开发端口（如 5173）创建一个公网URL。

设置Webhook: 运行一个一次性的脚本或通过浏览器访问一个临时创建的GET路由，调用 bot.api.setWebhook('你的ngrok链接/api/webhook/你的别名')，将Telegram的更新指向你的本地电脑。

执行测试: 在你自己的一个测试群组中，将Bot设为管理员，然后发送 /initialize_group 指令，观察你的本地控制台日志和群组内的变化。
