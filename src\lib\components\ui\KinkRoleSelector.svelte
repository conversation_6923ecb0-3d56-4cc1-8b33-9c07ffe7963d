<script lang="ts">
	import { Block, BlockTitle } from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import { KINK_CATEGORY_BITMASK } from '$lib/constants/kinks';
	import type { KinkCategoryBitmask } from '$lib/constants/kinks';
	import ChipGroup from './ChipGroup.svelte'; // 假设 ChipGroup 已被改造为回调模式

	type Props = {
		value?: bigint | null;
		// ✅ 1. 定义 onChange 回调函数属性
		onChange: (newValue: bigint | null) => void;
	};

	// ✅ 2. 移除 $bindable，接收 onChange prop
	let { value = null, onChange }: Props = $props();

	// --- 辅助函数保持不变 ---
	function bigintToRoles(bitmaskValue: bigint | null): KinkCategoryBitmask[] {
		const roles: KinkCategoryBitmask[] = [];
		if (bitmaskValue) {
			for (const role of Object.keys(KINK_CATEGORY_BITMASK) as KinkCategoryBitmask[]) {
				if ((bitmaskValue & BigInt(KINK_CATEGORY_BITMASK[role])) !== 0n) {
					roles.push(role);
				}
			}
		}
		return roles;
	}
	function rolesToBigint(roles: KinkCategoryBitmask[]): bigint | null {
		if (!roles || roles.length === 0) return null;
		const bitmask = roles.reduce((acc, role) => acc | BigInt(KINK_CATEGORY_BITMASK[role]), 0n);
		return bitmask === 0n ? null : bitmask;
	}

	// ✅ 3. 核心改动：使用 $derived 从传入的 value prop 派生出 UI 状态
	//    这完美地替代了之前用于“从父到子”同步的 $effect
	let selectedRoles = $derived(bigintToRoles(value));

	// ✅ 4. 创建一个简单的处理函数，替代之前“从子到父”同步的 $effect
	function handleChipChange(newSelectedRoles: KinkCategoryBitmask[]) {
		const newBitmaskValue = rolesToBigint(newSelectedRoles);
		// 调用回调，将新值“报告”给父组件
		onChange(newBitmaskValue);
	}

	// --- items 准备逻辑不变 ---
	const roleItems = (Object.keys(KINK_CATEGORY_BITMASK) as KinkCategoryBitmask[]).map((role) => ({
		value: role,
		label: m[`data_kink_role_${role}`]()
	}));

</script>

<BlockTitle>{m.routes_onboarding_label_kink_roles()}</BlockTitle>
<Block strong inset>
	<p class="mb-4 text-sm text-gray-500">{m.routes_onboarding_kink_roles_description()}</p>

	<!-- ✅ 5. 使用我们改造好的 ChipGroup 组件 -->
	<ChipGroup
		items={roleItems}
		multiple={true}
		value={selectedRoles}
		onChange={handleChipChange}
	/>
</Block>