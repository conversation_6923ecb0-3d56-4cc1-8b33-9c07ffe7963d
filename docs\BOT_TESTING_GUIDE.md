# Bot Testing Guide

## Overview
This guide covers how to test the CipherX TMA Telegram Bot system locally and in production.

## Prerequisites

### 1. Environment Setup
- Node.js 18+ installed
- pnpm package manager
- PostgreSQL database running
- Telegram bot tokens configured

### 2. Required Tools
- [ngrok](https://ngrok.com/) for local webhook testing
- Telegram account for testing
- Test Telegram supergroup

## Local Development Testing

### 1. Start Development Server
```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev --open --host
```

### 2. Setup ngrok Tunnel
```bash
# Install ngrok globally
npm install -g ngrok

# Start ngrok tunnel (in separate terminal)
ngrok http 5173

# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
```

### 3. Configure Environment
Update your `.env` file:
```env
WEBHOOK_BASE_URL="https://your-ngrok-url.ngrok.io"
BOT_TOKEN_DEV_BOT="your-development-bot-token"
BOT_ADMIN_IDS="your-telegram-user-id"
```

### 4. Set Webhook
```bash
# Set webhook for development bot
tsx scripts/setup-webhook.ts set dev-bot

# Verify webhook is set
tsx scripts/setup-webhook.ts info dev-bot
```

## Testing Scenarios

### 1. Basic Bot Functionality

#### Test /start Command
1. Message your bot directly
2. Send `/start` command
3. Verify welcome message appears

**Expected Response:**
```
🤖 CipherX Community Bot

Welcome! I'm here to help manage community groups.

Available Commands:
• /start - Show this welcome message
• /help - Get help information
• /initialize_group - Initialize a new community group (Admin only)
...
```

#### Test /help Command
1. Send `/help` command
2. Verify help documentation appears

### 2. Webhook Health Check

#### Test API Endpoint
Visit: `https://your-ngrok-url.ngrok.io/api/webhook/dev-bot`

**Expected Response:**
```json
{
  "success": true,
  "bot": {
    "id": 123456789,
    "username": "your_bot_username",
    "first_name": "Your Bot Name",
    "is_bot": true
  },
  "webhook": {
    "alias": "dev-bot",
    "endpoint": "/api/webhook/dev-bot"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 3. Group Initialization Testing

#### Setup Test Group
1. Create a new Telegram supergroup
2. Add your bot to the group
3. Promote bot to administrator with required permissions:
   - ✅ Manage Topics
   - ✅ Pin Messages
   - ✅ Edit Messages

#### Test Group Initialization
1. In the test group, send:
   ```
   /initialize_group slug=test-group country_code=US theme=test-theme province_code=CA
   ```

2. Verify the following happens:
   - ✅ Bot responds with "🚀 Initialization started. Please wait..."
   - ✅ Group description is updated
   - ✅ Standard topics are created:
     - 📢 Announcements & Rules
     - 💬 General Chat
     - 👋 Introductions
   - ✅ Regional topics are created:
     - CA｜California
     - TX｜Texas
     - FL｜Florida
     - NY｜New York
   - ✅ Welcome message is posted and pinned in Announcements
   - ✅ Bot responds with "✅ Initialization complete! The community is ready."

#### Test Error Scenarios
1. **Non-admin user**: Have a non-admin user try the command
   - Expected: Permission denied message

2. **Missing parameters**: Send incomplete command
   ```
   /initialize_group slug=test
   ```
   - Expected: Parameter validation error

3. **Invalid country code**: Use unsupported country
   ```
   /initialize_group slug=test country_code=XX theme=test
   ```
   - Expected: Configuration not found error

4. **Insufficient bot permissions**: Remove bot admin permissions
   - Expected: Permission validation error

## Debugging and Troubleshooting

### 1. Check Server Logs
Monitor your development server console for:
- Webhook processing logs
- Error messages
- Bot command execution logs

### 2. Webhook Debugging
```bash
# Check webhook status
tsx scripts/setup-webhook.ts info dev-bot

# Remove and reset webhook if needed
tsx scripts/setup-webhook.ts remove dev-bot
tsx scripts/setup-webhook.ts set dev-bot
```

### 3. Common Issues

#### Bot Not Responding
- ✅ Check bot token is correct
- ✅ Verify ngrok tunnel is active
- ✅ Check webhook URL is accessible
- ✅ Review server logs for errors

#### Permission Errors
- ✅ Ensure bot is administrator in group
- ✅ Verify required permissions are enabled
- ✅ Check user ID is in admin list

#### Topic Creation Fails
- ✅ Verify group is a supergroup (not regular group)
- ✅ Check bot has "Manage Topics" permission
- ✅ Ensure group has topics enabled

## Performance Testing

### 1. Rate Limiting
Test multiple rapid commands to verify rate limiting works:
```bash
# Send multiple commands quickly
/start
/help
/start
/help
```

### 2. Concurrent Group Initialization
Test initializing multiple groups simultaneously to check for race conditions.

### 3. Large Group Testing
Test with groups that have many members to verify performance.

## Production Testing

### 1. Pre-deployment Checklist
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates valid
- [ ] Webhook URLs use HTTPS
- [ ] Rate limiting configured
- [ ] Monitoring set up

### 2. Production Webhook Setup
```bash
# Set production webhooks
tsx scripts/setup-webhook.ts set usa-main-bot
tsx scripts/setup-webhook.ts set china-community-bot

# Verify all webhooks
tsx scripts/setup-webhook.ts list
```

### 3. Smoke Tests
1. Test basic bot commands in production
2. Verify webhook endpoints are accessible
3. Test group initialization with real groups
4. Monitor logs for errors

## Automated Testing

### 1. Unit Tests (Future Enhancement)
```bash
# Run unit tests (when implemented)
pnpm test
```

### 2. Integration Tests (Future Enhancement)
```bash
# Run integration tests (when implemented)
pnpm test:integration
```

## Monitoring and Alerts

### 1. Key Metrics to Monitor
- Webhook response times
- Command success/failure rates
- Group initialization success rates
- Error rates and types

### 2. Log Analysis
Monitor logs for:
- Failed webhook deliveries
- Permission errors
- API rate limit hits
- Database connection issues

### 3. Health Checks
Set up automated health checks:
- Bot API connectivity
- Webhook endpoint availability
- Database connectivity
- Command processing

## Rollback Procedures

### 1. Emergency Rollback
If issues are detected in production:

1. **Remove problematic webhooks:**
   ```bash
   tsx scripts/setup-webhook.ts remove usa-main-bot
   ```

2. **Revert to previous version**
3. **Restore webhooks with stable version**

### 2. Gradual Rollout
For new features:
1. Test with development bot first
2. Deploy to staging environment
3. Test with limited production groups
4. Full production rollout

## Support and Debugging

### 1. Log Collection
When reporting issues, include:
- Server logs with timestamps
- Webhook delivery logs
- Bot command execution logs
- Environment configuration (without secrets)

### 2. Issue Reproduction
Provide steps to reproduce:
1. Exact commands used
2. Group configuration
3. Bot permissions
4. Expected vs actual behavior

### 3. Emergency Contacts
- Development team contact information
- Escalation procedures
- Emergency rollback contacts
