# CipherX TMA - Environment Variables Example
# Copy this file to .env and fill in your actual values

# =================================================================
# DATABASE CONFIGURATION
# =================================================================
DATABASE_URL="postgres://root:mysecretpassword@localhost:5432/local"

# =================================================================
# TELEGRAM BOT CONFIGURATION
# =================================================================

# Bot Tokens - Use aliases for security
# Format: BOT_TOKEN_{ALIAS_IN_UPPERCASE}
# Example aliases: USA_MAIN_BOT, CHINA_COMMUNITY_BOT, etc.

# Main US Bot Token
BOT_TOKEN_USA_MAIN_BOT="123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"

# China Community Bot Token
BOT_TOKEN_CHINA_COMMUNITY_BOT="987654321:ZYXWVUTSRQPONMLKJIHGFEDCBAzyxwvutsrq"

# Development/Test Bot Token
BOT_TOKEN_DEV_BOT="555666777:TestTokenForDevelopmentOnlyNotForProduction"

# Legacy bot token (for backward compatibility)
BOT_TOKEN=""

# =================================================================
# BOT ADMINISTRATION
# =================================================================

# Comma-separated list of Telegram user IDs who can use admin commands
# Get your Telegram user ID from @userinfobot
BOT_ADMIN_IDS="123456789,987654321,555666777"

# =================================================================
# SECURITY SETTINGS
# =================================================================

# Rate limiter secret
RATE_LIMITER_SECRET="1d1e38a97761c94da9d9854e6c587619f50561d940e0bc1ca14cfe8a98699dcd"

# Webhook secret (optional, for additional security)
WEBHOOK_SECRET="your-webhook-secret-key-here"

# Application secret key for sessions and encryption
APP_SECRET="your-super-secret-app-key-change-this-in-production"

# =================================================================
# WEBHOOK CONFIGURATION
# =================================================================

# Base URL for webhooks (your domain)
# Example: https://yourdomain.com or https://your-ngrok-url.ngrok.io
WEBHOOK_BASE_URL="https://yourdomain.com"

# =================================================================
# APPLICATION CONFIGURATION
# =================================================================

# Environment (development, staging, production)
NODE_ENV="development"

# =================================================================
# LOGGING AND MONITORING
# =================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL="info"

# Enable detailed bot logging
BOT_DEBUG="false"

# =================================================================
# DEVELOPMENT ONLY
# =================================================================

# ngrok URL for local development webhooks
# NGROK_URL="https://your-ngrok-url.ngrok.io"

# Enable development features
DEV_MODE="true"
