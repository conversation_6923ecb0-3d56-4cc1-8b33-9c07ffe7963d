<script lang="ts">
	import { Block, BlockTitle, List, ListInput, Segmented, Button } from 'konsta/svelte';
	import { getOnboardingFormContext } from '$lib/contexts/onboardingForm';
	import { formFieldProxy } from 'sveltekit-superforms';
	import { KinkRatingsSelector, KinkRoleSelector } from '$components';
	import * as m from '$lib/paraglide/messages';

	const superform = getOnboardingFormContext();
	const { form: formStore, submitting } = superform;

	// ✅ 为本页字段使用 proxy
	const { value: kinkCategoryBitmask } = formFieldProxy(superform, 'kinkCategoryBitmask');
	const { value: bio, errors: bioErrors } = formFieldProxy(superform, 'bio');

	// 接收父组件传来的页面切换函数
	let { onSwitchPage }: { onSwitchPage: () => void } = $props();
</script>

<BlockTitle>{m.routes_onboarding_kink_profile_title()}</BlockTitle>
<Block strong inset>
	<p class="mb-4 text-sm text-gray-500">{m.routes_onboarding_kink_profile_description()}</p>
</Block>

<!-- ✅ 使用简洁的 bind:value 绑定到 proxy store -->
<KinkRoleSelector value={$kinkCategoryBitmask} onChange={(newValue) => ($kinkCategoryBitmask = newValue)} />

<List strong inset>
	<ListInput
		label={m.routes_onboarding_label_bio()}
		name="bio"
		value={$bio}
		error={$bioErrors?.[0]}
	/>
</List>

<!-- ✅ 对于 kinkRatings 这种复杂对象，保持“单向数据流+回调”模式是最稳健的 -->
<KinkRatingsSelector
	value={$formStore.kinkRatings}
	onChange={(newValue) => {
		$formStore.kinkRatings = newValue;
	}}
/>

<Segmented class="p-4">
	<Button type="button" disabled={$submitting} onclick={onSwitchPage}>
		{m.routes_onboarding_tab_basic()}
	</Button>
	<Button large tonal type="submit" disabled={$submitting}>
		{#if $submitting}
			{m.common_state_saving()}
		{:else}
			{m.routes_onboarding_button_complete()}
		{/if}
	</Button>
</Segmented>
