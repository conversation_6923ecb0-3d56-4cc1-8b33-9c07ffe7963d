<script lang="ts">
	import { State, Country, City } from 'country-state-city';
	import type { ICountry, IState, ICity } from 'country-state-city';
	import Svelecte from 'svelecte';
	import * as m from '$lib/paraglide/messages';

	type Props = {
		countryCode?: string;
		provinceCode?: string;
		city?: string;
		initialLanguage?: string;
	};

	let {
		countryCode = $bindable(),
		provinceCode = $bindable(),
		city = $bindable(),
		initialLanguage = 'en'
	}: Props = $props();

	const allCountries: ICountry[] = Country.getAllCountries();

	const statesOfCountry: IState[] = $derived(
		countryCode ? State.getStatesOfCountry(countryCode) : []
	);
	const cityOfState: ICity[] = $derived(
		countryCode && provinceCode ? City.getCitiesOfState(countryCode, provinceCode) : []
	);

	// Effect for initialization (this part was okay)
	$effect.pre(() => {
		if (!countryCode && initialLanguage) {
			const country = allCountries.find(
				(c) => c.isoCode.toLowerCase() === initialLanguage.toLowerCase()
			);
			if (country) {
				countryCode = country.isoCode;
			}
		}
	});

	// ✅ FIX #2: Correctly clear province and city when country changes
	let prevCountryCode = countryCode;
	$effect(() => {
		if (countryCode !== prevCountryCode) {
			provinceCode = undefined;
			city = undefined; // Also clear city when country changes
			prevCountryCode = countryCode;
		}
	});

	// ✅ FIX #2: Correctly clear city when province changes
	let prevProvinceCode = provinceCode;
	$effect(() => {
		if (provinceCode !== prevProvinceCode) {
			city = undefined;
			prevProvinceCode = provinceCode;
		}
	});
</script>

<div class="space-y-4">
	<div>
		<Svelecte
			options={allCountries}
			labelField="name"
			valueField="isoCode"
			placeholder={m.components_country_selector_placeholder_country()}
			bind:value={countryCode}
			searchable={true}
		/>
	</div>

	<div>
		<Svelecte
			options={statesOfCountry}
			labelField="name"
			valueField="isoCode"
			placeholder={m.components_country_selector_placeholder_province()}
			bind:value={provinceCode}
			disabled={statesOfCountry.length === 0}
			searchable={true}
		/>
	</div>

	<div>
		<Svelecte
			options={cityOfState}
			labelField="name"
			valueField="name"
			placeholder={m.components_country_selector_placeholder_city()}
			bind:value={city}
			disabled={cityOfState.length === 0}
			searchable={true}
		/>
	</div>
</div>
