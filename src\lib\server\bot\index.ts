// src/lib/server/bot/index.ts
// CipherX TMA - Bot 实例初始化和管理中心 (最终修正版)

import { Bot } from 'grammy';
import { getAllActiveBots, getBotInstance } from './botManager';
import { registerInitializeGroupCommand } from './commands/initializeGroup';

// 我们需要一个追踪器，来记录哪些别名的Bot已经被“训练”过了
const initializedBots = new Set<string>();

/**
 * 训练Bot实例：为它注册所有指令和处理器。
 * 这个函数现在只负责“训练”，不负责创建。
 */
function trainBotInstance(bot: Bot, tokenAlias: string): void {
	// 注册 /initialize_group 指令
	registerInitializeGroupCommand(bot);

	// 设置基本的消息处理
	setupBasicHandlers(bot);

	// 标记这个别名的Bot为已训练
	initializedBots.add(tokenAlias);

	console.log(`✅ Bot instance for alias [${tokenAlias}] has been trained and configured.`);
}
function setupBasicHandlers(bot: Bot): void {
	bot.on('message', async (ctx, next) => {
		console.log(`--- 📬 GRAMMY RECEIVED A MESSAGE ---`);
		console.log(`Chat Type: ${ctx.chat.type}`);
		console.log(`From User ID: ${ctx.from?.id}`);
		console.log(`Message Text: ${ctx.message.text || '(No text)'}`);
		await next();
	});

	// 处理 /start 指令
	bot.command('start', async (ctx) => {
		// ⬇️⬇️⬇️ 已修正的欢迎消息文本 ⬇️⬇️⬇️
		const welcomeMessage = `
🤖 *CipherX Community Bot*

Welcome\\! I'm here to help manage community groups\\.

*Available Commands:*
• \`/start\` \\- Show this welcome message
• \`/help\` \\- Get help information
• \`/initialize_group\` \\- Initialize a new community group \\(Admin only\\)

*For Administrators:*
To initialize a group, use:
\`/initialize_group slug=your\\-slug country_code=US theme=your\\-theme \\[province_code=CA\\]\`

Need help? Contact the administrators\\.
        `;

		await ctx.reply(welcomeMessage, { parse_mode: 'MarkdownV2' });
	});

	// 处理 /help 指令
	bot.command('help', async (ctx) => {
		// ⬇️⬇️⬇️ 已修正的帮助消息文本 ⬇️⬇️⬇️
		const helpMessage = `
📚 *Help & Documentation*

*Group Initialization:*
The \`/initialize_group\` command sets up a new community group with:
• Standard discussion topics
• Regional topics based on country/province
• Welcome message and rules
• Proper group configuration

*Parameters:*
• \`slug\` \\- Unique identifier for the group
• \`country_code\` \\- Country code \\(US, CN, etc\\.\\)
• \`theme\` \\- Group theme/category
• \`province_code\` \\- Optional province/state code

*Example:*
\`/initialize_group slug=cipherx\\-us\\-msm country_code=US theme=men\\-seeking\\-men province_code=CA\`

*Requirements:*
• Bot must be an administrator
• User must have admin permissions
• Group must be a supergroup

For technical support, contact the development team\\.
        `;

		await ctx.reply(helpMessage, { parse_mode: 'MarkdownV2' });
	});

	// 处理未知指令 (这个部分无需修改)
	bot.on('message:text', async (ctx, next) => {
		const text = ctx.message.text;
		if (
			text.startsWith('/') &&
			!['start', 'help', 'initialize_group'].some((cmd) => text.startsWith(`/${cmd}`))
		) {
			await ctx.reply('❓ Unknown command. Use /help to see available commands.', {
				reply_to_message_id: ctx.message.message_id
			});
			return;
		}
		await next();
	});

	// 全局错误处理 (这个部分无需修改)
	bot.catch((err) => {
		console.error('Bot error:', err);
	});

	console.log('✅ Basic handlers registered');
}

/**
 * 获取一个功能完备、训练有素的Bot实例
 * 这是被外部调用的主要函数
 */
export async function getInitializedBot(tokenAlias: string): Promise<Bot | null> {
	try {
		// 步骤1: 从引擎室获取一个原始的Bot实例 (可能来自缓存)
		const bot = await getBotInstance(tokenAlias);
		if (!bot) {
			console.error(`Failed to get bot instance for alias: ${tokenAlias}`);
			return null;
		}

		// 步骤2: 检查这个Bot实例是否已经被训练过
		if (!initializedBots.has(tokenAlias)) {
			// 如果没训练过，就进行一次“训练”
			trainBotInstance(bot, tokenAlias);
		}

		// 步骤3: 返回这个保证被训练过的Bot实例
		return bot;
	} catch (error) {
		console.error(`Error getting initialized bot for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 在应用启动时，预热并训练所有的Bot实例
 */
export async function initializeAllBots(): Promise<void> {
	try {
		console.log('🚀 Starting all bots initialization...');
		const activeBots = await getAllActiveBots();

		for (const botConfig of activeBots) {
			await getInitializedBot(botConfig.tokenAlias);
		}

		console.log(`✅ All ${activeBots.length} bots have been initialized and are ready.`);
	} catch (error) {
		console.error('❌ Error during all bots initialization:', error);
	}
}
