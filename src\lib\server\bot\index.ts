// src/lib/server/bot/index.ts
// CipherX TMA - Bot 实例初始化和管理中心 (最终生产版)

import { Bot } from 'grammy';
import { getAllActiveBots, getBotInstance } from './botManager';
import { registerInitializeGroupCommand } from './commands/initializeGroup';
import { registerBasicCommands, setupBotCommands } from './handlers/basicCommands';
import { registerUserOnboardingHandlers } from './handlers/userOnboarding';

// 追踪器，记录哪些别名的Bot已经被“训练”过了
const initializedBots = new Set<string>();

/**
 * 训练Bot实例：为它注册所有指令和处理器
 */
async function trainBotInstance(bot: Bot, tokenAlias: string): Promise<void> {
	console.log(`▶️ Training bot instance for alias: ${tokenAlias}...`);

	// 注册所有指令和事件处理器
	registerInitializeGroupCommand(bot);
	registerBasicCommands(bot);
	registerUserOnboardingHandlers(bot);

	// 设置Bot的命令菜单
	await setupBotCommands(bot);

	// 标记为已训练
	initializedBots.add(tokenAlias);
	console.log(`✅ Bot instance for alias [${tokenAlias}] is now fully trained and configured.`);
}

/**
 * 获取一个功能完备、训练有素的Bot实例
 */
export async function getInitializedBot(tokenAlias: string): Promise<Bot | null> {
	try {
		const bot = await getBotInstance(tokenAlias);
		if (!bot) {
			return null;
		}
		if (!initializedBots.has(tokenAlias)) {
			await trainBotInstance(bot, tokenAlias);
		}
		return bot;
	} catch (error) {
		console.error(`Error getting initialized bot for alias ${tokenAlias}:`, error);
		return null;
	}
}

/**
 * 在应用启动时，预热并训练所有的Bot实例
 */
export async function initializeAllBots(): Promise<void> {
	try {
		console.log('🚀 Starting all bots initialization...');
		const activeBots = await getAllActiveBots();
		for (const botConfig of activeBots) {
			await getInitializedBot(botConfig.tokenAlias);
		}
		console.log(`✅ All ${activeBots.length} bots have been initialized and are ready.`);
	} catch (error) {
		console.error('❌ Error during all bots initialization:', error);
	}
}
