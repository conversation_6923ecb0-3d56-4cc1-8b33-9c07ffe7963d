<script lang="ts">
	import { devLog } from '$utils/dev/devLogger'; // 可选的日志工具
	type Props = {
		publicUrl?: string | null;
		telegramUrl?: string | null;
		name: string;
		size?: number;
		className?: string;
	};

	let { publicUrl = null, telegramUrl = null, name, size = 40, className = '' }: Props = $props();

	// --- 内部状态和逻辑 (与之前完全相同) ---
	type Status = 'loading' | 'loaded' | 'fallback';
	let status = $state<Status>('loading');
	let displaySrc = $state<string | null>(null);

	function getInitials(name: string): string {
		if (!name) return '?';
		const nameParts = name.trim().split(/\s+/);
		if (/[\u4e00-\u9fa5]/.test(name)) {
			return name.slice(-2);
		}
		if (nameParts.length > 1) {
			return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
		}
		return name.substring(0, 2).toUpperCase();
	}

	function generateHslColor(str: string): string {
	// 如果传入的字符串是 null, undefined, 或空字符串 ""
		if (!str) { 
            // 直接返回一个安全、中性的默认灰色
			return 'hsl(220, 10%, 80%)'; 
		}

        // 只有在 str 有效时，才执行哈希计算
		let hash = 0;
		for (let i = 0; i < str.length; i++) {
			hash = str.charCodeAt(i) + ((hash << 5) - hash);
		}
		const h = hash % 360;
		return `hsl(${h}, 50%, 60%)`;
	}

	const initials = $derived(getInitials(name));
	const fallbackColor = $derived(generateHslColor(name));

	$effect(() => {
		let isCancelled = false;
		status = 'loading';
		displaySrc = null;

		async function loadImage() {
			if (publicUrl) {
				displaySrc = publicUrl;
				return;
			}
			if (telegramUrl) {
				try {
					const response = await fetch(telegramUrl);
					if (!response.ok) throw new Error('Telegram URL fetch failed');
					const blob = await response.blob();
					if (!isCancelled) {
						displaySrc = URL.createObjectURL(blob);
						status = 'loaded';
					}
				} catch (error) {
					devLog({ log: 'Failed to fetch Telegram URL, proceeding to fallback.', error });
					if (!isCancelled) {
						status = 'fallback';
					}
				}
				return;
			}
			if (!isCancelled) {
				status = 'fallback';
			}
		}

		loadImage();

		return () => {
			isCancelled = true;
			if (displaySrc && displaySrc.startsWith('blob:')) {
				URL.revokeObjectURL(displaySrc);
			}
		};
	});

	function handleError() {
		devLog({ log: `Image failed to load from src: ${displaySrc}. Switching to fallback.` });
		if (displaySrc && displaySrc.startsWith('blob:')) {
			URL.revokeObjectURL(displaySrc);
		}
		displaySrc = null;
		status = 'fallback';
	}
</script>

<div
	class="inline-flex flex-shrink-0 items-center justify-center overflow-hidden rounded-full align-middle select-none {className}"
	style:width="{size}px"
	style:height="{size}px"
	style:font-size="{size * 0.4}px"
>
	{#if status === 'loading'}
		<div class="h-full w-full animate-pulse bg-gray-200 dark:bg-zinc-700"></div>
	{:else if status === 'loaded' && displaySrc}
		<enhanced:img
			src={displaySrc}
			alt="{name}'s avatar"
			class="h-full w-full object-cover text-transparent"
			onload={() => (status = 'loaded')}
			onerror={handleError}
		/>
	{:else}
		<div
			class="flex h-full w-full items-center justify-center font-medium text-white"
			style:background-color={fallbackColor}
		>
			<span>{initials}</span>
		</div>
	{/if}
</div>
