// tailwind.config.cjs
const konstaConfig = require('konsta/config');

module.exports = konstaConfig({
	content: [
		'./src/**/*.{html,js,svelte,ts}',
		'./node_modules/konsta/**/*.{js,svelte}' // 确保 Konsta UI 的文件也被扫描
	],

	konsta: {
		// Konsta UI 的主要颜色配置
		colors: {
			// ✅ 关键：将 Konsta UI 的 primary 色设置为读取我们的 CSS 变量。
			// 这样，Konsta 组件如 <Button color="primary"> 就会使用 TMA 的主题色。
			primary: 'rgb(var(--app-primary-color) / <alpha-value>)',

			// 其他品牌色，Konsta UI 会将其转换为 `k-color-brand-red` 等类
			'brand-red': '#ff3b30',
			'brand-green': '#34c759',
			'brand-yellow': '#ffcc00'
		}
	},
	theme: {
		extend: {
			colors: {
				// ✅ 关键：扩展 Tailwind 的颜色，使其直接引用你的 CSS 变量。
				// 这样，你就可以在任何 HTML 元素或非 Konsta UI 组件上使用这些类。
				// 命名建议：可以加上 `app-` 或 `tma-` 前缀，以明确来源。
				'app-primary': 'rgb(var(--app-primary-color) / <alpha-value>)',
				'app-bg': 'rgb(var(--app-bg-color) / <alpha-value>)',
				'app-text': 'rgb(var(--app-text-color) / <alpha-value>)',
				'app-hint': 'rgb(var(--app-hint-color) / <alpha-value>)',
				'app-link': 'rgb(var(--app-link-color) / <alpha-value>)',
				'app-button': 'rgb(var(--app-button-color) / <alpha-value>)',
				'app-button-text': 'rgb(var(--app-button-text-color) / <alpha-value>)',
				'app-secondary-bg': 'rgb(var(--app-secondary-background-color) / <alpha-value>)',

				// TMA SDK 提供的其他颜色映射
				'app-header-bg': 'rgb(var(--app-header-background-color) / <alpha-value>)',
				'app-section-bg': 'rgb(var(--app-section-background-color) / <alpha-value>)',
				'app-section-separator': 'rgb(var(--app-section-separator-color) / <alpha-value>)',
				'app-section-header-text': 'rgb(var(--app-section-header-text-color) / <alpha-value>)',
				'app-subtitle-text': 'rgb(var(--app-subtitle-text-color) / <alpha-value>)',
				'app-destructive-text': 'rgb(var(--app-destructive-text-color) / <alpha-value>)'

				// 如果你需要 Tailwind 自己的标准颜色（如 red-500），且不希望它们受 Konsta/TMA 影响
				// 可以在这里引入 Tailwind 的默认颜色并使用它们，例如：
				// 'tw-red': colors.red[500],
				// 'tw-blue': colors.blue[500],
			}
		}
	},
	plugins: [],
	// Safelist 对于动态类名（例如根据数据渲染的颜色）很有用。
	// 对于固定映射到 CSS 变量的类，通常不是必须的，因为它们会在编译时被发现。
	// 但如果你在 Konsta 组件中使用了像 `color="brand-red"` 这样的属性，
	// KonstaConfig 应该会处理它们。如果 Konsta 的类没有被生成，可以将其加入 safelist。
	safelist: [
		// 确保 Konsta 的品牌色类被生成
		'k-color-brand-red',
		'k-color-brand-green',
		'k-color-brand-yellow'
		// 任何你在 Svelte/Konsta 组件中动态拼接，
		// 而非直接写在 class 属性中的 Tailwind 类，都应该加到这里。
	]
});