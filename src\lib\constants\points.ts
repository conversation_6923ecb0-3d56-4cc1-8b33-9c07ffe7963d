/**
 * CipherX 积分系统常量定义
 */

// 1. 定义所有合法的、程序可用的交易类型 Key
export const POINT_TRANSACTION_TYPES = {
	// 赚取积分
	REGISTRATION_BONUS: 'registration_bonus',
	PROFILE_COMPLETION_BONUS: 'profile_completion_bonus',
	DAILY_CHECK_IN: 'daily_check_in',
	INVITE_SUCCESS: 'invite_success',
	CONTENT_CONTRIBUTION: 'content_contribution', // 未来可用于奖励优质内容

	// 消耗积分
	GROUP_SUBSCRIPTION_FEE: 'group_subscription_fee',
	ADVANCED_SEARCH: 'advanced_search_cost', // 未来高级搜索可能消耗积分
	SUPER_LIKE: 'super_like_cost',

	// 其他
	ADMIN_ADJUSTMENT: 'admin_adjustment' // 管理员手动调整
} as const;

export type PointTransactionType =
	(typeof POINT_TRANSACTION_TYPES)[keyof typeof POINT_TRANSACTION_TYPES];

// 2. 定义不同行为对应的标准积分值
/**
 * 定义 Onboarding 阶段的用户积分奖励。
 */
export const ONBOARDING_REWARDS = {
	FULL: 100, // 资料完整度达到门槛（例如50分）时奖励
	PARTIAL: 10 // 资料未达到门槛，但仍参与Onboarding时奖励
} as const;

export const POINTS = {
	FOR_PROFILE_COMPLETION: ONBOARDING_REWARDS.FULL, // 引用新的Onboarding奖励常量
	FOR_DAILY_CHECK_IN: 10,
	FOR_INVITE_SUCCESS: 50,
	WEEKLY_GROUP_FEE: -10 // 消耗我们用负数表示
};
