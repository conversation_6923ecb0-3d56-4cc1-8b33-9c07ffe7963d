Init Data
The 💠component responsible for the Telegram Mini Apps init data.

Restoring
To restore the component state, use the restore method.

Variable

Functions

import { initData } from '@telegram-apps/sdk';

initData.restore();
Parsing
To parse the value as init data, use the parseInitData function.

import { parseInitData } from '@telegram-apps/sdk';

const initData = parseInitData('auth_date=123&query_id=anQQ231vs&...');
// {
// user: {
// id: 99281932,
// firstName: 'Andrew',
// lastName: 'Rogue',
// username: 'rogue',
// languageCode: 'en',
// isPremium: true,
// allowsWriteToPm: true,
// },
// hash: 'abcedef123',
// authDate: Date(1716922846000),
// startParam: 'debug',
// chatType: 'sender',
// chatInstance: '8428209589180549439',
// };
The function returns the init data object with deeply camel-cased properties.

Signals
This section provides a complete list of signals related to the init data.

authDate
Return type: Date | undefined

The date the initialization data was created.

Variable

Functions

import { initDataAuthDate } from '@telegram-apps/sdk';

initDataAuthDate(); // Date(1727368894000)
canSendAfter
Return type: number | undefined

The number of seconds after which a message can be sent via the method answerWebAppQuery.

Variable

Functions

initData.canSendAfter(); // 3600
canSendAfterDate
Return type: Date | undefined

canSendAfter but as a Date.

Variable

Functions

initData.canSendAfterDate(); // Date(1727368897600)
chat
Return type: undefined or Chat with camel-cased properties.

An object containing data about the chat where the bot was launched via the attachment menu.

NOTE

Returned for supergroups, channels and group chats – only for Mini Apps launched via the attachment menu.

Variable

Functions

initData.chat();
// {
// id: 7728725378876215,
// type: 'group',
// title: '@BotFather',
// photoUrl: 'https://example.com/image.png',
// username: 'botfather'
// }
chatType
Return type: string | undefined

The type of chat from which the Mini Apps was opened. Values:

sender
private
group
supergroup
channel
NOTE

Returned only for applications opened by direct link.

Variable

Functions

initData.chatType(); // 'group'
chatInstance
Return type: string | undefined

A global identifier indicating the chat from which the Mini Apps was opened.

WARNING

Returned only for applications opened by direct link.

Variable

Functions

initData.chatInstance(); // 'group'
hash
Return type: string | undefined

Initialization data signature.

Variable

Functions

initData.hash(); // 'group'
queryId
Return type: string | undefined

The unique session ID of the Mini App. Used in the process of sending a message via the method answerWebAppQuery.

Variable

Functions

initData.queryId(); // 'group'
raw
Return type: string | undefined

A raw string representation of the initialization data.

Variable

Functions

initData.raw(); // 'user=...&chat=...&...'
receiver
Return type: undefined or User with camel-cased properties.

An object containing data about the chat partner of the current user in the chat where the bot was launched via the attachment menu.

NOTE

Returned only for private chats and only for Mini Apps launched via the attachment menu.

Variable

Functions

initData.user();
// {
// addedToAttachmentMenu: false,
// allowsWriteToPm: true,
// isPremium: true,
// firstName: 'Pavel',
// id: 78262681,
// isBot: false,
// lastName: 'Durov',
// languageCode: 'ru',
// photoUrl: 'https://example.com/image.png',
// username: 'durove',
// }
state
Return type: undefined or InitData with deeply camel-cased properties.

An object containing the initialization data in object format.

Variable

Functions

initData.state();
startParam
Return type: string | undefined

The value of the startattach or startapp query parameter specified in the link.

Variable

Functions

initData.startParam(); // 'my-value'
user
Return type: undefined or User with camel-cased properties.

An object containing information about the current user.

Variable

Functions

initData.user();
// {
// addedToAttachmentMenu: false,
// allowsWriteToPm: true,
// isPremium: true,
// firstName: 'Pavel',
// id: 78262681,
// isBot: false,
// lastName: 'Durov',
// languageCode: 'ru',
// photoUrl: 'https://example.com/image.png',
// username: 'durove',
// }
