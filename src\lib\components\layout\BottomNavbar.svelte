<script lang="ts">
	import { Toolbar, Link } from 'konsta/svelte';
	import { page } from '$app/state'; // 正确的导入方式
	import { ScanSearch, Heart, Wallet, ShieldUser } from '@lucide/svelte';

	// 定义导航项
	const navItems = [
		{ href: '/discover', icon: ScanSearch },
		{ href: '/likes', icon: Heart },
		{ href: '/points', icon: Wallet },
		{ href: '/profile', icon: ShieldUser }
	];

	// 使用 $derived 来响应式地获取当前路径
	let currentPathname = $derived(page.url.pathname);

	// 检查当前链接是否活跃的函数
	function isActive(href: string, pathname: string): boolean {
		if (href === '/') {
			return pathname === href;
		}
		// 对于嵌套路由，startsWith 更可靠
		return pathname.startsWith(href);
	}
</script>

<Toolbar
	bottom
	class="h-14 border-t border-gray-200 bg-white px-2 py-3 dark:border-zinc-700 dark:bg-zinc-800"
>
	{#each navItems as item}
		<Link
			href={item.href}
			class="flex h-full w-full flex-col items-center justify-center
                   text-gray-500 transition-colors duration-200
                   ease-in-out dark:text-gray-400
                   "
		>
			<item.icon
				size={24}
				class="{isActive(item.href, currentPathname)
					? 'text-blue-600 dark:text-blue-400' // 活跃状态颜色
					: ''} mb-0.5"
			/>
		</Link>
	{/each}
</Toolbar>
