import { json, error, type RequestEvent } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { validate, parse } from '@telegram-apps/init-data-node';
import { BOT_TOKEN } from '$env/static/private';
import { lucia } from '$lib/server/auth';
import { findOrCreateUser } from '$lib/server/services/user.service';
import { telegramUserSchema } from '$lib/schemas/auth.schema';
import { ZodError } from 'zod';
import { isInitialOnboardingRequired } from '$utils/user/user.utils';
import { devLog } from '$utils/dev/devLogger';
import { AUTHORIZATION_TMA_PREFIX } from '$lib/constants';
import { superJson } from '$lib/utils';

export const POST: RequestHandler = async (event: RequestEvent) => {
	const { request, cookies } = event;
	const authHeader = request.headers.get('Authorization');

	if (!authHeader || !authHeader.startsWith(AUTHORIZATION_TMA_PREFIX)) {
		return new Response('Unauthorized: Invalid Authorization header', { status: 401 });
	}

	const initDataRaw = authHeader.slice(AUTHORIZATION_TMA_PREFIX.length);
	if (!initDataRaw) {
		throw error(400, 'initData is required');
	}

	try {
		// 1. 验证 InitData
		validate(initDataRaw, BOT_TOKEN, { expiresIn: 3600 });
		const parsedData = parse(initDataRaw);
		const telegramUser = telegramUserSchema.parse(parsedData.user);

		// ✅ 2. 查找或创建用户。我们的 findOrCreateUser 函数现在会返回一个 DisplayUserProfile 对象
		const userProfile = await findOrCreateUser(telegramUser, parsedData.start_param);
		devLog({ userProfileFromDb: userProfile });

		// 3. 创建或验证 Lucia Session
		// (您的 session 处理逻辑非常完善，保持不变)
		let session = null;
		const existingSessionId = cookies.get(lucia.sessionCookieName);
		if (existingSessionId) {
			const validationResult = await lucia.validateSession(existingSessionId);
			session = validationResult.session;
			if (!session || session.userId !== userProfile.id) {
				session = await lucia.createSession(userProfile.id, {});
			}
		} else {
			session = await lucia.createSession(userProfile.id, {});
		}

		// 4. 设置 Session Cookie
		const sessionCookie = lucia.createSessionCookie(session.id);
		cookies.set(sessionCookie.name, sessionCookie.value, {
			path: '/',
			...sessionCookie.attributes
		});

		// ✅ 5. 关键改动：准备最终返回给前端的完整数据包
		const userNeedsOnboarding = isInitialOnboardingRequired(userProfile);

		// 在 JSON 响应中，同时返回成功状态、Onboarding 需求、以及完整的用户信息
		return superJson({
			success: true,
			userNeedsOnboarding: userNeedsOnboarding,
			userProfile: userProfile
		});
	} catch (e) {
		if (e instanceof ZodError) {
			console.error('Authentication failed: Zod validation error', e.errors);
			throw error(400, 'Invalid user data format.');
		} else if (e instanceof Error && e.message.includes('Invalid hash')) {
			console.error('Authentication failed: Invalid initData hash', e);
			throw error(401, 'Invalid authentication data.');
		} else if (e instanceof Error && e.message.includes('Expired initData')) {
			console.error('Authentication failed: Expired initData', e);
			throw error(401, 'Authentication data expired.');
		} else {
			console.error('Authentication failed: An unexpected error occurred', e);
			throw error(500, 'Authentication failed due to an internal server error.');
		}
	}
};
