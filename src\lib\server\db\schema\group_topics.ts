// src/lib/server/db/schema/group_topics.ts
// CipherX TMA - 话题资产表定义 (最终版)

import { pgTable, text, bigint, integer, timestamp, uniqueIndex } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { groups } from './groups';
import { users } from './users';
import { topicStatusEnum, topicTypeEnum } from './_enums';

/**
 * 群组话题表 - 社区的核心内容结构资产
 */
export const groupTopics = pgTable(
	'group_topics',
	{
		// 内部主键
		id: text('id').primaryKey(),

		// Telegram 关联信息
		telegramTopicId: bigint('telegram_topic_id', { mode: 'number' }).unique().notNull(),
		groupId: text('group_id')
			.notNull()
			.references(() => groups.id, { onDelete: 'cascade' }),

		// 核心元数据
		name: text('name').notNull(),
		type: topicTypeEnum('type').notNull().default('standard'),
		status: topicStatusEnum('status').notNull().default('active'),

		// 统计与分析数据
		messageCount: integer('message_count').notNull().default(0),
		lastActivityAt: timestamp('last_activity_at', { withTimezone: true }).notNull().defaultNow(),

		// 管理信息
		creatorId: text('creator_id')
			.notNull()
			.references(() => users.id),

		// 时间戳
		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => {
		// 确保同一个群组内，话题名称是唯一的
		return [uniqueIndex('unique_name_per_group_idx').on(table.groupId, table.name)];
	}
);

// --- 关系定义 ---

export const groupTopicsRelations = relations(groupTopics, ({ one }) => ({
	group: one(groups, { fields: [groupTopics.groupId], references: [groups.id] }),
	creator: one(users, { fields: [groupTopics.creatorId], references: [users.id] })
}));
