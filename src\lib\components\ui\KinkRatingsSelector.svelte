<script lang="ts">
	import { Block, BlockTitle, Range } from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import { KINK_RATINGS } from '$lib/constants/kinks';
	import type { KinkRatings, KinkRatingValue } from '$lib/constants/kinks';
	import { getRatingLabel, getRatingBadgeColor } from '$utils/ui/kinkRatings';

	type Props = {
		value?: Record<string, number> | null;
		disabled?: boolean;
		onChange: (newValue: Record<string, number>) => void;
	};

	let { value = null, disabled = false, onChange = () => {} }: Props = $props();
	let collapsed = $state(true);
	// Initialize ratings if not provided (确保 value 始终是对象)
	if (!value) {
		value = {};
	}

	function updateRating(interest: KinkRatings, rating: KinkRatingValue) {
		if (disabled) return;

		const currentRatings = { ...(value || {}) };
		let newRatings: Record<string, number>;
		const currentRatingForInterest = currentRatings[interest];

		if (rating === 0 || currentRatingForInterest === rating) {
			// 如果点击的是 0，或者重复点击同一个评分，则移除
			delete currentRatings[interest];
			newRatings = currentRatings;
		} else {
			// 否则，更新为新的评分
			newRatings = { ...currentRatings, [interest]: rating };
		}

		onChange(newRatings);
	}

	function getRating(interest: KinkRatings): KinkRatingValue {
		return (value?.[interest] as KinkRatingValue) ?? 0;
	}

	// Prepare interest items for display
	const interestItems = KINK_RATINGS.map((interest) => ({
		key: interest,
		label: m[`data_kink_interest_${interest}`](),
		description: m[`data_kink_interest_${interest}_description`]
			? m[`data_kink_interest_${interest}_description`]()
			: ''
	}));

	// Count of rated interests
	let ratedCount = $derived(Object.keys(value || {}).length);
</script>

<Block strong inset>
	<button
		type="button"
		class="flex w-full items-center justify-between p-4 text-left"
		onclick={() => (collapsed = !collapsed)}
	>
		<div>
			<BlockTitle class="mb-0">{m.routes_onboarding_label_kink_interests()}</BlockTitle>
			<p class="mt-1 text-sm text-gray-500">
				{ratedCount > 0
					? m.routes_onboarding_kink_interests_rated_count({ count: ratedCount })
					: m.routes_onboarding_kink_interests_description()}
			</p>
		</div>
		<svg
			class="h-5 w-5 transition-transform duration-200 {collapsed ? '' : 'rotate-180'}"
			fill="none"
			stroke="currentColor"
			viewBox="0 0 24 24"
		>
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"
			></path>
		</svg>
	</button>

	{#if !collapsed}
		<div class="px-4 pb-4">
			<div class="mb-4 rounded-lg border border-amber-200 bg-amber-50 p-3">
				<p class="text-sm text-amber-800">
					<strong>{m.routes_onboarding_kink_privacy_notice_title()}:</strong>
					{m.routes_onboarding_kink_privacy_notice_description()}
				</p>
			</div>

			<div class="space-y-6">
				{#each interestItems as item (item.key)}
					{@const currentRating = getRating(item.key)}
					<div class="border-b border-gray-100 pb-4 last:border-b-0">
						<div class="mb-2 flex items-center justify-between">
							<div>
								<h4 class="font-medium text-gray-900">{item.label}</h4>
								{#if item.description}
									<p class="text-sm text-gray-500">{item.description}</p>
								{/if}
							</div>
							<div class="text-right">
								<span class="text-sm font-medium {getRatingBadgeColor(currentRating)}">
									{getRatingLabel(currentRating)}
								</span>
							</div>
						</div>

						<div class="flex items-center space-x-2">
							{#each [-2, -1, 0, 1, 2, 3, 4, 5] as rating}
								<button
									type="button"
									class="flex-1 rounded px-1 py-2 text-xs transition-colors duration-200 {currentRating ===
										rating && rating !== 0 // 选中状态判断，0不再被视为“选中”
										? getRatingBadgeColor(rating) + '  font-medium'
										: 'text-gray-400 hover:text-gray-600'} {disabled
										? 'cursor-not-allowed opacity-50'
										: 'cursor-pointer'}"
									onclick={() => updateRating(item.key, rating as KinkRatingValue)}
									{disabled}
								>
									{rating > 0 ? '+' : ''}{rating}
								</button>
							{/each}
						</div>
					</div>
				{/each}
			</div>

			<div class="mt-6 rounded-lg border border-red-200 bg-red-50 p-4">
				<h4 class="mb-2 font-medium text-red-800">{m.routes_onboarding_kink_safety_title()}</h4>
				<p class="mb-2 text-sm text-red-700">
					{m.routes_onboarding_kink_safety_ssc_description()}
				</p>
				<p class="text-sm text-red-600">
					{m.routes_onboarding_kink_safety_disclaimer()}
				</p>
			</div>
		</div>
	{/if}
</Block>
