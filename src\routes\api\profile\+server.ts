import { error } from '@sveltejs/kit';
import { getDisplayUserProfile } from '$lib/server/services/user.service';
import type { RequestHandler } from './$types';
import { superJson } from '$lib/utils';

export const GET: RequestHandler = async ({ locals }) => {
	const { user } = locals;
	if (!user) throw error(401, 'Unauthorized');

	const userProfile = await getDisplayUserProfile(user.id);
	if (!userProfile) throw error(404, 'User profile not found');

	// ✅ 直接返回序列化后的用户对象
	return superJson(userProfile);
};
