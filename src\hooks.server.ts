import { sequence } from '@sveltejs/kit/hooks';
import { lucia } from '$lib/server/auth';
import { redirect, type Handle } from '@sveltejs/kit';
import { paraglideMiddleware } from '$lib/paraglide/server';
import type { HandleServerError } from '@sveltejs/kit';
import { devLog } from '$utils/dev/devLogger';

const handleParaglide: Handle = ({ event, resolve }) =>
	paraglideMiddleware(event.request, ({ request, locale }) => {
		event.request = request;

		return resolve(event, {
			transformPageChunk: ({ html }) => html.replace('%paraglide.lang%', locale)
		});
	});

const handleAuth: Handle = async ({ event, resolve }) => {
	const sessionId = event.cookies.get(lucia.sessionCookieName);
	let user = null;
	let session = null;

	if (sessionId) {
		try {
			const validationResult = await lucia.validateSession(sessionId);
			session = validationResult.session;
			user = validationResult.user;

			if (session && session.fresh) {
				const sessionCookie = lucia.createSessionCookie(session.id);
				event.cookies.set(sessionCookie.name, sessionCookie.value, {
					path: '.',
					...sessionCookie.attributes
				});
			} else if (!session) {
				const sessionCookie = lucia.createBlankSessionCookie();
				event.cookies.set(sessionCookie.name, sessionCookie.value, {
					path: '.',
					...sessionCookie.attributes
				});
			}
		} catch (e) {
			console.error('Error validating session in hooks.server.ts:', e);
			// In case of error, ensure session is null and a blank cookie is set
			session = null;
			user = null;
			const sessionCookie = lucia.createBlankSessionCookie();
			event.cookies.set(sessionCookie.name, sessionCookie.value, {
				path: '.',
				...sessionCookie.attributes
			});
		}
	}

	event.locals.user = user;
	// devLog({ user });
	event.locals.session = session;
	// Redirect logic for protected routes
	if (event.route.id?.startsWith('/(protected)')) {
		if (!user) {
			throw redirect(303, '/');
		}
	}

	// Specific check for onboarding
	if (event.route.id === '/(public)/onboarding') {
		if (!user) {
			throw redirect(303, '/');
		}
	}

	return resolve(event);
};

export const handleError: HandleServerError = ({ error, event }) => {
	// 将错误信息记录到您的监控服务
	console.error('An unexpected error occurred:', {
		error,
		user: event.locals.user?.id,
		route: event.route.id
	});
	// Sentry.captureException(error, { extra: { ... } });

	// 可以向用户返回一个不包含敏感信息的、通用的错误消息
	return {
		message: '服务器开小差了，请稍后再试 T_T',
		code: 'INTERNAL_ERROR'
	};
};

export const handle: Handle = sequence(handleParaglide, handleAuth);
