<script lang="ts">
	import { Block, Card, Progressbar } from 'konsta/svelte';
	import { calculateProfileCompleteness } from '$utils/user/user.utils';
	import type { DisplayUserProfile } from '$lib/schemas/profile.schema';
	import { m } from '../../paraglide/messages';

	type Props = {
		profileData: Partial<DisplayUserProfile>;
	};

	let { profileData }: Props = $props();

	const completenessScore = $derived(calculateProfileCompleteness(profileData));
</script>

<!-- Profile Completeness -->
<Block class="!py-1">
	<Card class="!p-2">
		<div class="flex items-center justify-between">
			<div>
				<h3 class="font-medium text-gray-900">{m.routes_profile_completeness_title()}</h3>
				<p class="text-sm text-gray-600">{m.routes_profile_completeness_description()}</p>
			</div>
			<div class="text-right">
				<div class="text-2xl font-bold text-blue-600">{completenessScore}%</div>
				<div class="mt-1 h-2 w-16 rounded-full bg-gray-200">
					<div
						class="h-full rounded-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300"
						style="width: {completenessScore}%"
					></div>
				</div>
			</div>
		</div>
	</Card>
</Block>
