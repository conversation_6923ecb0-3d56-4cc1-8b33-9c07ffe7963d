// src/lib/schemas/common.schema.ts
// CipherX TMA - 通用原子验证规则库
// 职责：定义应用中可被复用的、最基础的Zod schema。
// 所有错误消息都使用 i18n key，以供 ParaglideJS 进行国际化。

import { z } from 'zod';

/**
 * 验证由 nanoid 生成的、带前缀的ID。
 * @param prefix - ID应有的前缀, e.g., ID_PREFIX_USER
 */
export const createIdSchema = (prefix: string) =>
	z
		.string({ required_error: 'lib_validation_id_required' })
		.startsWith(prefix, `lib_validation_id_prefix_invalid`);

/**
 * 验证 Telegram User ID 的格式
 */
export const telegramUserIdSchema = z
	.bigint({ required_error: 'lib_validation_telegram_id_required' })
	.positive({ message: 'lib_validation_telegram_id_invalid' });

/**
 * 验证 Kink Map 分享码的格式
 */
export const kinkMapCodeSchema = z
	.string({ required_error: 'lib_validation_kink_map_code_required' })
	.length(10, 'lib_validation_kink_map_code_length');

/**
 * 验证用户昵称的基础规则
 */
export const nicknameSchema = z
	.string({ required_error: 'lib_validation_nickname_required' })
	.trim()
	.min(2, 'lib_validation_nickname_too_short')
	.max(50, 'lib_validation_nickname_too_long');

/**
 * 验证 Telegram 用户名的格式 (可选)
 */
export const telegramUsernameSchema = z
	.string()
	.min(2)
	.max(32)
	.regex(/^[a-zA-Z0-9_]+$/, 'lib_validation_telegram_username_format');

/**
 * 验证个人简介
 */
export const bioSchema = z.string().trim().max(500, 'lib_validation_bio_too_long');

/**
 * 验证年龄 (使用 coerce 自动转换字符串为数字)
 */
export const ageSchema = z.coerce
	.number({ invalid_type_error: 'lib_validation_age_must_be_number' })
	.int('lib_validation_age_must_be_integer')
	.min(18, 'lib_validation_age_too_young')
	.max(99, 'lib_validation_age_invalid');

/**
 * 验证身高 (cm)
 */
export const heightSchema = z.coerce
	.number({ invalid_type_error: 'lib_validation_height_must_be_number' })
	.int()
	.min(100, 'lib_validation_height_out_of_range')
	.max(250, 'lib_validation_height_out_of_range');

/**
 * 验证体重 (kg)
 */
export const weightSchema = z.coerce
	.number({ invalid_type_error: 'lib_validation_weight_must_be_number' })
	.int()
	.min(30, 'lib_validation_weight_out_of_range')
	.max(300, 'lib_validation_weight_out_of_range');

/**
 * 验证国家代码 (ISO 3166-1 alpha-2)
 */
export const countryCodeSchema = z
	.string()
	.length(2, 'lib_validation_country_code_invalid')
	.toUpperCase();

/**
 * 验证省份/州代码
 */
export const provinceCodeSchema = z.string().min(1).max(10);

/**
 * 验证城市名称
 */
export const citySchema = z.string().min(1, 'lib_validation_city_required').max(100);

/**
 * 验证语言代码 (ISO 639-1)
 */
export const languageCodeSchema = z.string().min(2).toLowerCase();

/**
 * 验证 TON 钱包地址 (基本格式检查)
 */
export const tonWalletAddressSchema = z
	.string()
	.regex(/^[A-Za-z0-9_-]{48}$/, 'lib_validation_ton_address_invalid');

/**
 * 验证 Kink 评分值的范围
 */
export const kinkRatingsValueSchema = z
	.number()
	.int()
	.min(-2, 'lib_validation_rating_invalid')
	.max(5, 'lib_validation_rating_invalid');

export const urlSchema = z.string().url({ message: 'lib_validation_url_invalid' });