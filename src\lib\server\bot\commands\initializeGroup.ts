// src/lib/server/bot/commands/initializeGroup.ts
// CipherX TMA - 群组初始化指令处理器 (最终重构版 v2.1)

import type { CommandContext, Context } from 'grammy';
import { db } from '$lib/server/db';
import { bots, groups, groupTranslations, users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import fs from 'node:fs/promises';
import path from 'node:path';
import { isAdmin } from '$lib/server/bot/botManager';
import { groupTopics } from '$lib/server/db/schema/group_topics';
import type { TelegramTopicIconColor } from '$lib/constants';
// --- 接口定义 (Interfaces) ---

interface InitializeGroupParams {
	slug: string;
	country_code: string;
	theme: string;
}

interface CountryConfig {
	countryName: string;
	defaultLang: string;
	description: string;
	welcomeMessage: string;
	standardTopics: Array<{
		id: string;
		name: string;
		emoji: string;
		iconColor: TelegramTopicIconColor;
	}>;
	provinces: Array<{
		code: string;
		name: string;
	}>;
	provinceTopicDefaults: {
		emoji: string;
		iconColor: TelegramTopicIconColor;
	};
}

// --- 主要的指令处理器 (Main Command Handler) ---

export function registerInitializeGroupCommand(bot: any): void {
	bot.command('initialize_group', initializeGroupHandler);
}

async function initializeGroupHandler(ctx: CommandContext<Context>): Promise<void> {
	try {
		// 1. 解析与快速验证
		const params = parseCommandParams(ctx.match as string);
		if (!params) {
			/* ... error handling ... */ return;
		}

		if (!(await validateUserPermissions(ctx)) || !(await validateBotPermissions(ctx))) {
			return;
		}

		const countryConfig = await loadCountryConfig(params.country_code);
		if (!countryConfig) {
			/* ... error handling ... */ return;
		}

		const existingGroup = await db.query.groups.findFirst({
			where: eq(groups.telegramChatId, ctx.chat!.id)
		});
		if (existingGroup) {
			await ctx.reply(
				`ℹ️ This group has already been initialized with slug: "${existingGroup.slug}". Aborting.`
			);
			return;
		}

		// 2. 立即响应，并将长任务放入后台
		await ctx.reply('✅ Command received. Initialization will run in the background...');

		executeInitializationSteps(ctx, params, countryConfig)
			.then(async () => {
				await ctx.api.sendMessage(
					ctx.from!.id,
					`🎉 Success! The group "${ctx.chat?.title}" has been fully initialized.`
				);
			})
			.catch(async (error) => {
				console.error(`❌ Background initialization for slug "${params.slug}" failed:`, error);
				await ctx.api.sendMessage(
					ctx.from!.id,
					`🔥 Error! Initialization for group "${ctx.chat?.title}" failed.`
				);
			});
	} catch (error) {
		console.error('Error in initializeGroupHandler (pre-execution):', error);
		await ctx.reply(
			`❌ An immediate error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

// --- 核心执行流程 (Core Execution Flow) ---

/**
 * 核心执行流程 (V3.3 - 修正了执行顺序和参数传递)
 */
async function executeInitializationSteps(
	ctx: CommandContext<Context>,
	params: InitializeGroupParams,
	config: CountryConfig
) {
	const chat = await ctx.getChat();
	const groupId = nanoid(); // 预先生成群组的内部ID

	// ✅ 步骤一：在上层统一获取 creatorId
	const creatorTelegramId = ctx.from!.id;
	const systemUser = await db.query.users.findFirst({
		where: eq(users.telegramUserId, BigInt(creatorTelegramId))
	});
	if (!systemUser) {
		throw new Error(
			`User with Telegram ID ${creatorTelegramId} not found. Admin must register in TMA first.`
		);
	}
	const creatorId = systemUser.id; // 这就是我们需要的、唯一的创建者ID

	await updateChatDescription(ctx, chat, config);
	// ✅ 步骤二：立刻将群组主信息存入数据库
	await saveGroupToDatabase(ctx, groupId, params, config, creatorId);

	// ✅ 步骤三：现在可以安全地创建话题，并将 creatorId 传递下去
	const { announcementTopicId } = await createStandardTopics(ctx, groupId, config, creatorId);
	await createRegionalTopics(ctx, groupId, config, creatorId);

	// ✅ 步骤四：用获取到的话题ID，更新群组记录
	if (announcementTopicId) {
		await db
			.update(groups)
			.set({ announcementTopicId: announcementTopicId })
			.where(eq(groups.id, groupId));
	}

	// ✅ 步骤五：执行剩余的非数据库关联操作
	if (announcementTopicId) {
		await postWelcomeMessage(ctx, config, announcementTopicId);
	}
	await setDefaultPermissions(ctx);
}

// --- 步骤实现 (Step Implementations) ---

async function updateChatDescription(
	ctx: CommandContext<Context>,
	chat: Awaited<ReturnType<typeof ctx.getChat>>,
	config: CountryConfig
): Promise<void> {
	const newDescription = config.description;
	if (chat.description !== newDescription) {
		await ctx.api.setChatDescription(ctx.chat!.id, newDescription);
	}
}
/**
 * 步骤2: 创建标准话题 (V3.1 - 接收 creatorId)
 */
async function createStandardTopics(
	ctx: CommandContext<Context>,
	groupId: string,
	config: CountryConfig,
	creatorId: string // ✅ 接收 creatorId 作为参数
): Promise<{ announcementTopicId?: number }> {
	let announcementTopicId: number | undefined;
	for (const topic of config.standardTopics) {
		const topicFullName = `${topic.emoji} ${topic.name}`;
		const createdTopic = await ctx.api.createForumTopic(ctx.chat!.id, topicFullName, {
			icon_color: topic.iconColor
		});

		await db.insert(groupTopics).values({
			id: nanoid(),
			telegramTopicId: createdTopic.message_thread_id,
			groupId: groupId,
			name: topicFullName,
			type: 'standard',
			creatorId: creatorId // ✅ 使用传入的 creatorId
		});

		if (topic.id === 'announcements') {
			announcementTopicId = createdTopic.message_thread_id;
		}
	}
	return { announcementTopicId };
}

/**
 * 步骤3: 创建地区话题 (V3.1 - 接收 creatorId)
 */
async function createRegionalTopics(
	ctx: CommandContext<Context>,
	groupId: string,
	config: CountryConfig,
	creatorId: string // ✅ 接收 creatorId 作为参数
): Promise<void> {
	for (const province of config.provinces) {
		const topicFullName = `${config.provinceTopicDefaults.emoji} ${province.code}｜${province.name}`;
		const createdTopic = await ctx.api.createForumTopic(ctx.chat!.id, topicFullName, {
			icon_color: config.provinceTopicDefaults.iconColor
		});

		await db.insert(groupTopics).values({
			id: nanoid(),
			telegramTopicId: createdTopic.message_thread_id,
			groupId: groupId,
			name: topicFullName,
			type: 'province',
			creatorId: creatorId // ✅ 使用传入的 creatorId
		});
	}
}

async function postWelcomeMessage(
	ctx: CommandContext<Context>,
	config: CountryConfig,
	topicId: number
): Promise<void> {
	const welcomeMsg = await ctx.api.sendMessage(ctx.chat!.id, config.welcomeMessage, {
		message_thread_id: topicId,
		parse_mode: 'MarkdownV2'
	});
	await ctx.api.pinChatMessage(ctx.chat!.id, welcomeMsg.message_id);
}

async function setDefaultPermissions(ctx: CommandContext<Context>): Promise<void> {
	await ctx.api.setChatPermissions(ctx.chat!.id, {
		can_send_messages: true,
		can_send_voice_notes: true,
		can_send_other_messages: true,
		can_send_photos: false,
		can_send_videos: false,
		can_send_audios: false,
		can_send_documents: false,
		can_send_video_notes: false,
		can_send_polls: false,
		can_add_web_page_previews: false,
		can_change_info: false,
		can_invite_users: false,
		can_pin_messages: false,
		can_manage_topics: false
	});
}
/**
 * 步骤6: 保存群组信息到数据库 (V3.2 - 接收 creatorId)
 */
async function saveGroupToDatabase(
	ctx: CommandContext<Context>,
	groupId: string,
	params: InitializeGroupParams,
	config: CountryConfig,
	creatorId: string // ✅ 接收 creatorId 作为参数
): Promise<void> {
	// ... (botId 的逻辑保持不变)
	const botUsername = ctx.me.username;
	const botRecord = await db.query.bots.findFirst({ where: eq(bots.username, botUsername) });
	if (!botRecord) {
		throw new Error(`Bot with username "${botUsername}" not found in the database.`);
	}

	// 插入主表
	await db.insert(groups).values({
		id: groupId,
		slug: params.slug,
		telegramChatId: ctx.chat!.id,
		countryCode: params.country_code.toUpperCase(),
		theme: params.theme,
		creatorId: creatorId, // ✅ 使用传入的 creatorId
		botId: botRecord.id,
		memberCount: await ctx.getChatMemberCount()
	});

	// 插入翻译表
	await db.insert(groupTranslations).values({
		groupId: groupId,
		languageCode: config.defaultLang,
		name: config.countryName,
		description: config.description
	});

	console.log(`📝 Group "${params.slug}" and its translation saved to database.`);
}
// --- 工具与验证函数 (Utilities & Validation) ---

/**
 * 解析指令参数
 */
function parseCommandParams(commandText: string): InitializeGroupParams | null {
	const params: Partial<InitializeGroupParams> = {};
	const paramString = commandText.replace('/initialize_group', '').trim();
	if (!paramString) return null;

	const paramPairs = paramString.split(/\s+/);
	for (const pair of paramPairs) {
		const [key, value] = pair.split('=');
		if (key && value && ['slug', 'country_code', 'theme'].includes(key.trim())) {
			params[key.trim() as keyof InitializeGroupParams] = value.trim();
		}
	}

	if (!params.slug || !params.country_code || !params.theme) {
		return null;
	}
	return params as InitializeGroupParams;
}

/**
 * 验证用户权限
 */
async function validateUserPermissions(ctx: CommandContext<Context>): Promise<boolean> {
	if (!ctx.from?.id) {
		await ctx.reply('❌ Unable to identify user. Please try again.');
		return false;
	}
	if (!isAdmin(ctx.from.id)) {
		await ctx.reply('❌ You do not have permission to use this command.');
		return false;
	}
	return true;
}

/**
 * 验证Bot权限
 */
async function validateBotPermissions(ctx: CommandContext<Context>): Promise<boolean> {
	if (ctx.chat?.type !== 'supergroup') {
		await ctx.reply('❌ This command can only be used in supergroups.');
		return false;
	}

	const botMember = await ctx.getChatMember(ctx.me.id);
	if (botMember.status !== 'administrator') {
		await ctx.reply('❌ Bot must be an administrator to initialize the group.');
		return false;
	}

	const requiredPermissions: (keyof typeof botMember)[] = ['can_manage_topics', 'can_pin_messages'];
	for (const permission of requiredPermissions) {
		if (!botMember[permission]) {
			await ctx.reply(`❌ Bot is missing required permission: ${permission}.`);
			return false;
		}
	}
	return true;
}

/**
 * 加载国家配置 (使用Node.js原生模块以保证可靠性)
 */
async function loadCountryConfig(countryCode: string): Promise<CountryConfig | null> {
	try {
		const filePath = path.join(
			process.cwd(),
			'config',
			'countries',
			`${countryCode.toUpperCase()}.json`
		);
		const fileContent = await fs.readFile(filePath, 'utf-8');
		return JSON.parse(fileContent) as CountryConfig;
	} catch (error) {
		console.error(`Error loading country config for ${countryCode}:`, error);
		return null;
	}
}
