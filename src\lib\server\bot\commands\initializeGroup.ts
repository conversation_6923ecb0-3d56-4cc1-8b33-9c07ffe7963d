// src/lib/server/bot/commands/initializeGroup.ts
// CipherX TMA - 群组初始化指令处理器

import type { CommandContext, Context } from 'grammy';
import { isAdmin } from '$lib/server/bot/botManager';
import { db } from '$lib/server/db';
import { groups } from '$lib/server/db/schema';
import { nanoid } from 'nanoid';
import { devLog } from '../../../utils';

/**
 * 指令参数接口
 */
interface InitializeGroupParams {
	slug: string;
	country_code: string;
	theme: string;
	province_code?: string;
}

/**
 * 国家配置接口
 */
interface CountryConfig {
	countryName: string;
	defaultLang: string;
	standardTopics: Array<{
		id: string;
		name: string;
	}>;
	provinces: Array<{
		code: string;
		name: string;
	}>;
	welcomeMessage: string;
}

/**
 * 解析指令参数
 * 支持格式: /initialize_group slug=cipherx-us-msm country_code=US theme=men-seeking-men province_code=CA
 */
function parseCommandParams(commandText: string): InitializeGroupParams | null {
	try {
		// 移除指令名称，只保留参数部分
		const paramString = commandText.replace('/initialize_group', '').trim();

		if (!paramString) {
			return null;
		}

		// 解析参数
		const params: Partial<InitializeGroupParams> = {};
		const paramPairs = paramString.split(/\s+/);

		for (const pair of paramPairs) {
			const [key, value] = pair.split('=');
			if (key && value) {
				switch (key.trim()) {
					case 'slug':
						params.slug = value.trim();
						break;
					case 'country_code':
						params.country_code = value.trim().toUpperCase();
						break;
					case 'theme':
						params.theme = value.trim();
						break;
					case 'province_code':
						params.province_code = value.trim().toUpperCase();
						break;
				}
			}
		}

		// 验证必需参数
		if (!params.slug || !params.country_code || !params.theme) {
			return null;
		}

		return params as InitializeGroupParams;
	} catch (error) {
		console.error('Error parsing command parameters:', error);
		return null;
	}
}

/**
 * 验证用户权限
 */
async function validateUserPermissions(ctx: CommandContext<Context>): Promise<boolean> {
	try {
		// 检查用户是否为管理员
		if (!ctx.from?.id) {
			await ctx.reply('❌ Unable to identify user. Please try again.');
			return false;
		}

		if (!isAdmin(ctx.from.id)) {
			await ctx.reply(
				'❌ You do not have permission to use this command. Only administrators can initialize groups.'
			);
			return false;
		}

		return true;
	} catch (error) {
		console.error('Error validating user permissions:', error);
		await ctx.reply('❌ Error validating permissions. Please try again.');
		return false;
	}
}

/**
 * 验证Bot权限
 */
async function validateBotPermissions(ctx: CommandContext<Context>): Promise<boolean> {
	try {
		// 检查是否在群组中
		if (ctx.chat?.type !== 'supergroup') {
			await ctx.reply('❌ This command can only be used in supergroups.');
			return false;
		}

		// 获取Bot在群组中的状态
		const botMember = await ctx.getChatMember(ctx.me.id);

		if (botMember.status !== 'administrator') {
			await ctx.reply(
				'❌ Bot must be an administrator to initialize the group. Please promote the bot and try again.'
			);
			return false;
		}

		// 检查关键权限
		const requiredPermissions = ['can_manage_topics', 'can_pin_messages', 'can_edit_messages'];

		for (const permission of requiredPermissions) {
			if (!(botMember as any)[permission]) {
				await ctx.reply(
					`❌ Bot is missing required permission: ${permission}. Please update bot permissions and try again.`
				);
				return false;
			}
		}

		return true;
	} catch (error) {
		console.error('Error validating bot permissions:', error);
		await ctx.reply(
			'❌ Error checking bot permissions. Please ensure the bot is an administrator.'
		);
		return false;
	}
}

/**
 * 加载国家配置
 */
async function loadCountryConfig(countryCode: string): Promise<CountryConfig | null> {
	try {
		// 动态导入配置文件
		const configModule = await import(`../../../../../config/countries/${countryCode}.json`);
		return configModule.default as CountryConfig;
	} catch (error) {
		console.error(`Error loading country config for ${countryCode}:`, error);
		return null;
	}
}

/**
 * 主要的群组初始化处理器
 */
export async function initializeGroupHandler(ctx: CommandContext<Context>): Promise<void> {
	try {
		devLog({ log: `--- 🚀 [${new Date().toISOString()}] COMMAND HANDLER TRIGGERED ---` });
		devLog({ log: `Command received from user: ${ctx.from?.id} in chat: ${ctx.chat?.id}` });
		// 1. 解析指令参数
		const params = parseCommandParams(ctx.match as string);
		if (!params) {
			await ctx.reply(
				'❌ Invalid command format. Please use:\n' +
					'/initialize_group slug=your-slug country_code=US theme=your-theme [province_code=CA]'
			);
			return;
		}

		// 2. 验证用户权限
		if (!(await validateUserPermissions(ctx))) {
			return;
		}

		// 3. 验证Bot权限
		if (!(await validateBotPermissions(ctx))) {
			return;
		}

		// 4. 加载国家配置
		const countryConfig = await loadCountryConfig(params.country_code);
		if (!countryConfig) {
			await ctx.reply(`❌ Configuration not found for country code: ${params.country_code}`);
			return;
		}

		// 5. 开始初始化流程
		await ctx.reply('🚀 Initialization started. Please wait...');

		// 6. 执行初始化步骤（将在下一个任务中实现）
		await executeInitializationSteps(ctx, params, countryConfig);
	} catch (error) {
		console.error('Error in initializeGroupHandler:', error);
		await ctx.reply(
			`❌ An error occurred during initialization: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * 执行初始化步骤
 */
async function executeInitializationSteps(
	ctx: CommandContext<Context>,
	params: InitializeGroupParams,
	countryConfig: CountryConfig
): Promise<void> {
	let announcementTopicId: number | undefined;

	try {
		await ctx.reply('Debug: Step 1 - Setting description...');
		// 1. 设置群组描述
		await ctx.api.setChatDescription(
			ctx.chat!.id,
			'Welcome! This group is currently under automated setup.'
		);
		await ctx.reply('✅ Group description updated');

		// 2. 创建标准话题并记录公告话题ID
		await ctx.reply('📝 Creating standard topics...');
		for (const topic of countryConfig.standardTopics) {
			try {
				const createdTopic = await ctx.api.createForumTopic(ctx.chat!.id, topic.name);
				console.log(`Created topic: ${topic.name} with ID: ${createdTopic.message_thread_id}`);

				if (topic.id === 'announcements') {
					announcementTopicId = createdTopic.message_thread_id;
				}

				// 添加小延迟避免API限制
				await new Promise((resolve) => setTimeout(resolve, 500));
			} catch (error) {
				console.error(`Error creating topic ${topic.name}:`, error);
				await ctx.reply(`⚠️ Warning: Could not create topic "${topic.name}"`);
			}
		}

		// 3. 创建地区话题
		await ctx.reply('🌍 Creating regional topics...');
		for (const province of countryConfig.provinces) {
			try {
				const topicName = `${province.code}｜${province.name}`;
				await ctx.api.createForumTopic(ctx.chat!.id, topicName);
				console.log(`Created regional topic: ${topicName}`);

				// 添加小延迟避免API限制
				await new Promise((resolve) => setTimeout(resolve, 500));
			} catch (error) {
				console.error(`Error creating regional topic ${province.name}:`, error);
				await ctx.reply(`⚠️ Warning: Could not create regional topic "${province.name}"`);
			}
		}

		// 4. 发布并置顶欢迎语
		if (announcementTopicId) {
			try {
				await ctx.reply('📢 Publishing welcome message...');
				const welcomeMsg = await ctx.api.sendMessage(ctx.chat!.id, countryConfig.welcomeMessage, {
					message_thread_id: announcementTopicId
				});

				await ctx.api.pinChatMessage(ctx.chat!.id, welcomeMsg.message_id);
				console.log('Welcome message posted and pinned');
			} catch (error) {
				console.error('Error posting welcome message:', error);
				await ctx.reply('⚠️ Warning: Could not post or pin welcome message');
			}
		}

		// 5. 数据库持久化
		await saveGroupToDatabase(ctx, params, announcementTopicId);

		// 6. 成功反馈
		await ctx.reply('✅ Initialization complete! The community is ready.');
	} catch (error) {
		console.error('Error in executeInitializationSteps:', error);
		throw error;
	}
}

/**
 * 保存群组信息到数据库
 */
async function saveGroupToDatabase(
	ctx: CommandContext<Context>,
	params: InitializeGroupParams,
	announcementTopicId?: number
): Promise<void> {
	try {
		// 注意：这里需要一个真实的用户ID作为创建者
		// 在实际使用中，应该从认证系统获取对应的用户ID
		// 这里使用占位符，实际部署时需要修改
		const creatorUserId = 'placeholder-user-id'; // TODO: 从认证系统获取真实用户ID

		const groupData = {
			id: nanoid(),
			slug: params.slug,
			telegramChatId: ctx.chat!.id,
			nameEn: `${params.country_code} Community - ${params.theme}`,
			nameZh:
				params.country_code === 'CN' ? `${params.country_code} 社区 - ${params.theme}` : undefined,
			countryCode: params.country_code,
			provinceCode: params.province_code || null,
			theme: params.theme,
			creatorId: creatorUserId,
			memberCount: 1
		};

		// 尝试插入数据库（标记为需要处理的数据库操作）
		// await db.insert(groups).values(groupData);

		console.log('📝 Database operation marked for manual handling:', {
			operation: 'INSERT INTO groups',
			data: groupData,
			announcementTopicId
		});

		await ctx.reply(
			'📝 Group information prepared for database storage (manual handling required)'
		);
	} catch (error) {
		console.error('Error saving group to database:', error);
		await ctx.reply('⚠️ Warning: Could not save group information to database');
	}
}

/**
 * 注册指令到Bot实例
 */
export function registerInitializeGroupCommand(bot: any): void {
	bot.command('initialize_group', initializeGroupHandler);
	console.log('✅ initialize_group command registered');
}
