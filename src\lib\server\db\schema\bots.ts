// src/lib/server/db/schema/bots.ts
// CipherX TMA - Telegram Bot 管理表定义

import { pgTable, text, timestamp, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { botStatusEnum } from './_enums';
import { users } from './users';

/**
 * Bot 表 - 管理 Telegram Bot 实例
 * 存储 Bot 的基本信息、Token 别名映射和状态管理
 */
export const bots = pgTable('bots', {
	// 程序化ID，用于内部关联
	id: text('id').primaryKey(),
	
	// Bot 的人类可读名称
	name: text('name').notNull(),
	
	// Token 别名，用于 URL 路径中的安全标识
	// 例如: 'usa-main-bot', 'china-community-bot'
	tokenAlias: text('token_alias').unique().notNull(),
	
	// 实际的 Telegram Bot Token (加密存储)
	// 注意: 在生产环境中应该加密存储
	telegramBotToken: text('telegram_bot_token').notNull(),
	
	// Bot 的用途描述
	description: text('description'),
	
	// Bot 状态
	status: botStatusEnum('status').notNull().default('active'),
	
	// Bot 创建者
	creatorId: text('creator_id')
		.notNull()
		.references(() => users.id),
	
	// 是否启用 Webhook
	webhookEnabled: boolean('webhook_enabled').notNull().default(true),
	
	// Webhook URL (如果使用)
	webhookUrl: text('webhook_url'),
	
	// 时间戳
	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
});

/**
 * Bot 管理员关系表
 * 定义哪些用户可以管理特定的 Bot
 */
export const botAdmins = pgTable('bot_admins', {
	botId: text('bot_id')
		.notNull()
		.references(() => bots.id, { onDelete: 'cascade' }),
	
	userId: text('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	
	// 管理员权限级别
	permissions: text('permissions').array().notNull().default(['basic']),
	
	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow()
});

// --- 关系定义 ---

export const botsRelations = relations(bots, ({ one, many }) => ({
	creator: one(users, {
		fields: [bots.creatorId],
		references: [users.id]
	}),
	admins: many(botAdmins)
}));

export const botAdminsRelations = relations(botAdmins, ({ one }) => ({
	bot: one(bots, {
		fields: [botAdmins.botId],
		references: [bots.id]
	}),
	user: one(users, {
		fields: [botAdmins.userId],
		references: [users.id]
	})
}));

// --- 类型导出 ---
export type Bot = typeof bots.$inferSelect;
export type NewBot = typeof bots.$inferInsert;

export type BotAdmin = typeof botAdmins.$inferSelect;
export type NewBotAdmin = typeof botAdmins.$inferInsert;
