// src/lib/server/db/schema/bots.ts (V2.0 - 安全简化版)

import { pgTable, text, timestamp, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { groups } from './groups';
import { botStatusEnum } from './_enums';

/**
 * Bot 表 - V2.0
 * 职责: 管理Bot实例的元数据、配置和业务关联
 */
export const bots = pgTable('bots', {
	// 内部主键
	id: text('id').primaryKey(),

	// Bot 的人类可读名称
	name: text('name').notNull(),

	// Token 别名，用于 Webhook URL 和 .env 查找
	tokenAlias: text('token_alias').unique().notNull(),
	username: text('username').unique().notNull(),
	// ⛔️ 移除了 telegramBotToken 字段，以遵循安全最佳实践

	// Bot 的用途描述
	description: text('description'),

	// ✅ 新增：Bot 负责的国家/地区代码，用于业务逻辑关联
	countryCode: text('country_code').unique(), // 一个国家通常只由一个Bot负责

	// Bot 状态
	status: botStatusEnum('status').notNull().default('active'),

	// Bot 创建者 (外键)
	creatorId: text('creator_id')
		.notNull()
		.references(() => users.id),

	// 时间戳
	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
	updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
});

/**
 * Bot 管理员关系表 - V2.0 (简化)
 * MVP阶段，我们可以依赖环境变量中的全局管理员。
 * 这张表可以作为未来更精细化权限管理的基础，暂时可以不创建。
 * 如果要创建，可以简化为只记录关系，权限逻辑放在代码中。
 */
export const botAdmins = pgTable(
	'bot_admins',
	{
		botId: text('bot_id')
			.notNull()
			.references(() => bots.id, { onDelete: 'cascade' }),
		userId: text('user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' })
	},
	(table) => {
		// 组合主键
		return [primaryKey({ columns: [table.botId, table.userId] })];
	}
);

// --- 关系定义 ---

export const botsRelations = relations(bots, ({ one, many }) => ({
	creator: one(users, {
		fields: [bots.creatorId],
		references: [users.id]
	}),
	admins: many(botAdmins),
	// ✅ 新增：一个Bot可以管理多个群组
	managedGroups: many(groups)
}));

export const botAdminsRelations = relations(botAdmins, ({ one }) => ({
	bot: one(bots, {
		fields: [botAdmins.botId],
		references: [bots.id]
	}),
	user: one(users, {
		fields: [botAdmins.userId],
		references: [users.id]
	})
}));

// --- 类型导出 ---
export type Bot = typeof bots.$inferSelect;
export type NewBot = typeof bots.$inferInsert;
