# Bot Security Setup Guide

## Overview
This guide covers the security configuration for the CipherX TMA Telegram Bot system.

## Environment Variables Setup

### 1. Copy Environment Template
```bash
cp .env.example .env
```

### 2. Configure Bot Tokens

#### Getting Bot Tokens
1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Use `/newbot` command to create a new bot
3. Follow the prompts to set bot name and username
4. Copy the provided token

#### Token Aliases
Use descriptive aliases for your bot tokens:
- `BOT_TOKEN_USA_MAIN_BOT` - Main US community bot
- `BOT_TOKEN_CHINA_COMMUNITY_BOT` - China community bot
- `BOT_TOKEN_DEV_BOT` - Development/testing bot

#### Example Configuration
```env
BOT_TOKEN_USA_MAIN_BOT="123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijk"
BOT_TOKEN_CHINA_COMMUNITY_BOT="987654321:ZYXWVUTSRQPONMLKJIHGFEDCBAzyxwvutsrq"
```

### 3. Configure Admin Users

#### Getting Your Telegram User ID
1. Message [@userinfobot](https://t.me/userinfobot) on Telegram
2. Copy your numeric user ID
3. Add to the admin list

#### Example Configuration
```env
BOT_ADMIN_IDS="123456789,987654321,555666777"
```

## Bot Permissions Setup

### Required Bot Permissions
When adding the bot to a group, ensure it has these permissions:

#### Essential Permissions
- ✅ **Administrator** - Required for all operations
- ✅ **Manage Topics** - Create and manage forum topics
- ✅ **Pin Messages** - Pin welcome messages
- ✅ **Edit Messages** - Modify group settings

#### Optional Permissions
- ✅ **Delete Messages** - Clean up spam/inappropriate content
- ✅ **Restrict Members** - Moderate users if needed
- ✅ **Invite Users** - Add members to the group

### Setting Up Bot Permissions
1. Add bot to your supergroup
2. Go to group settings → Administrators
3. Promote the bot to administrator
4. Enable required permissions listed above

## Webhook Configuration

### Local Development with ngrok
1. Install ngrok: `npm install -g ngrok`
2. Start your development server: `pnpm run dev`
3. In another terminal: `ngrok http 5173`
4. Copy the HTTPS URL from ngrok
5. Update your `.env` file:
   ```env
   WEBHOOK_BASE_URL="https://your-ngrok-url.ngrok.io"
   ```

### Production Deployment
1. Set your production domain:
   ```env
   WEBHOOK_BASE_URL="https://yourdomain.com"
   ```
2. Ensure HTTPS is properly configured
3. Set up webhook endpoints for each bot

## Security Best Practices

### 1. Token Security
- ❌ Never commit actual tokens to version control
- ✅ Use environment variables for all sensitive data
- ✅ Rotate tokens regularly
- ✅ Use different tokens for different environments

### 2. Admin Access
- ✅ Limit admin user list to trusted individuals
- ✅ Regularly review admin access
- ✅ Use principle of least privilege

### 3. Webhook Security
- ✅ Use HTTPS for all webhook endpoints
- ✅ Validate webhook requests
- ✅ Implement rate limiting
- ✅ Monitor for suspicious activity

### 4. Database Security
- ✅ Use strong database passwords
- ✅ Limit database access to necessary IPs
- ✅ Regular database backups
- ✅ Encrypt sensitive data at rest

## Testing Your Setup

### 1. Bot Health Check
Visit: `https://yourdomain.com/api/webhook/your-bot-alias`

Expected response:
```json
{
  "success": true,
  "bot": {
    "id": 123456789,
    "username": "your_bot_username",
    "first_name": "Your Bot Name",
    "is_bot": true
  },
  "webhook": {
    "alias": "your-bot-alias",
    "endpoint": "/api/webhook/your-bot-alias"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. Command Testing
1. Add bot to a test supergroup
2. Promote bot to administrator with required permissions
3. Test the `/start` command
4. Test the `/help` command
5. Test the `/initialize_group` command (admin only)

### 3. Webhook Testing
1. Send a message to the bot
2. Check server logs for webhook processing
3. Verify bot responds appropriately

## Troubleshooting

### Common Issues

#### Bot Not Responding
- Check bot token is correct
- Verify webhook URL is accessible
- Check server logs for errors

#### Permission Denied Errors
- Ensure bot is administrator in the group
- Verify required permissions are enabled
- Check user is in admin list

#### Webhook Errors
- Verify HTTPS is working
- Check webhook URL format
- Ensure server is accessible from Telegram

### Getting Help
- Check server logs for detailed error messages
- Test with development bot first
- Contact development team for support

## Production Checklist

Before deploying to production:

- [ ] All tokens are properly configured
- [ ] Admin user IDs are correct
- [ ] Webhook URLs use HTTPS
- [ ] Database is properly secured
- [ ] Rate limiting is configured
- [ ] Monitoring is set up
- [ ] Backup procedures are in place
- [ ] Security review completed
