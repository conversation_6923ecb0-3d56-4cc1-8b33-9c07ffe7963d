// src/lib/server/bot/handlers/basicCommands.ts
// CipherX TMA - 基础指令处理器

import type { CommandContext, Context } from 'grammy';
import { InlineKeyboard } from 'grammy';

/**
 * MarkdownV2 特殊字符转义
 */
function escapeMarkdownV2(text: string): string {
	// MarkdownV2 需要转义的特殊字符
	const specialChars = [
		'_',
		'*',
		'[',
		']',
		'(',
		')',
		'~',
		'`',
		'>',
		'#',
		'+',
		'-',
		'=',
		'|',
		'{',
		'}',
		'.',
		'!'
	];

	let escaped = text;
	specialChars.forEach((char) => {
		escaped = escaped.replace(new RegExp('\\' + char, 'g'), '\\' + char);
	});

	return escaped;
}

/**
 * 获取TMA应用的启动URL
 */
function getTMAAppUrl(): string {
	return 'https://t.me/sveltekit_bot/tmademo';
}
/**
 * /start 指令处理器
 */
export async function handleStartCommand(ctx: CommandContext<Context>): Promise<void> {
	try {
		const welcomeMessage = `
🤖 *CipherX Community Bot*

Welcome\\! I'm here to help manage community groups and guide you to our amazing features\\.

*Available Commands:*
• \`/start\` \\- Show this welcome message
• \`/help\` \\- Get detailed help information
• \`/initialize\\_group\` \\- Initialize a new community group \\(Admin only\\)

*Ready to explore?*
Click the button below to open our Telegram Mini App and discover the full CipherX experience\\!
		`;

		// 创建内联键盘
		const keyboard = new InlineKeyboard().url('🚀 Open CipherX App', getTMAAppUrl());

		await ctx.reply(welcomeMessage.trim(), {
			reply_markup: keyboard,
			parse_mode: 'MarkdownV2'
		});
	} catch (error) {
		console.error('Error in start command:', error);
		// 发送简单的备用消息
		await ctx.reply('🤖 Welcome to CipherX Community Bot! Use /help for more information.');
	}
}

/**
 * /help 指令处理器
 */
export async function handleHelpCommand(ctx: CommandContext<Context>): Promise<void> {
	try {
		const helpMessage = `
📚 *CipherX Bot Help & Documentation*

*For Regular Users:*
• \`/start\` \\- Welcome message and app access
• \`/help\` \\- This help information

*For Group Administrators:*
The \`/initialize\\_group\` command sets up a new community group with:
• Standard discussion topics
• Regional topics based on country/province
• Welcome message and rules
• Proper group configuration

*Command Format:*
\`/initialize\\_group slug=your\\-slug country\\_code=US theme=your\\-theme \\[province\\_code=CA\\]\`

*Parameters:*
• \`slug\` \\- Unique identifier for the group
• \`country\\_code\` \\- Country code \\(US, GB, JP, TW, DE, CN\\)
• \`theme\` \\- Group theme/category
• \`province\\_code\` \\- Optional province/state code

*Example:*
\`/initialize\\_group slug=cipherx\\-us\\-msm country\\_code=US theme=men\\-seeking\\-men province\\_code=CA\`

*Requirements for Group Initialization:*
• Bot must be an administrator
• User must have admin permissions
• Group must be a supergroup
• Bot needs permissions: Manage Topics, Pin Messages, Edit Messages

*Need More Help?*
Open our TMA app for full documentation and support\\.
		`;

		// 创建内联键盘
		const keyboard = new InlineKeyboard()
			.url('📱 Open TMA App', getTMAAppUrl())
			.row()
			.url('📖 Documentation', 'https://github.com/your-repo/docs'); // 替换为实际文档链接

		await ctx.reply(helpMessage.trim(), {
			reply_markup: keyboard,
			parse_mode: 'MarkdownV2'
		});
	} catch (error) {
		console.error('Error in help command:', error);
		// 发送简单的备用消息
		await ctx.reply(
			'📚 CipherX Bot Help\n\nUse /start to begin or contact administrators for support.'
		);
	}
}

/**
 * 处理未知指令
 */
export async function handleUnknownCommand(ctx: Context): Promise<void> {
	try {
		const text = ctx.message?.text;

		if (text?.startsWith('/')) {
			const unknownCommand = text.split(' ')[0];
			const message = `❓ Unknown command: \`${escapeMarkdownV2(unknownCommand)}\`\n\nUse /help to see available commands\\.`;

			await ctx.reply(message, {
				reply_to_message_id: ctx.message?.message_id,
				parse_mode: 'MarkdownV2'
			});
		}
	} catch (error) {
		console.error('Error handling unknown command:', error);
		// 发送简单的备用消息
		await ctx.reply('❓ Unknown command. Use /help to see available commands.');
	}
}

/**
 * 设置Bot命令菜单
 */
export async function setupBotCommands(bot: any): Promise<void> {
	try {
		const commands = [
			{
				command: 'start',
				description: 'Welcome message and app access'
			},
			{
				command: 'help',
				description: 'Get help and documentation'
			},
			{
				command: 'initialize_group',
				description: 'Initialize a new community group (Admin only)'
			}
		];

		await bot.api.setMyCommands(commands);
		console.log('✅ Bot commands menu set successfully');
	} catch (error) {
		console.error('Error setting bot commands:', error);
	}
}

/**
 * 注册调试用的全局消息处理器
 */
function registerDebugHandlers(bot: any): void {
	bot.on('message', async (ctx: Context, next: any) => {
		console.log(`--- 📬 GRAMMY RECEIVED A MESSAGE ---`);
		console.log(`Chat Type: ${ctx.chat?.type}, From User ID: ${ctx.from?.id}`);
		console.log(`Message Text: ${ctx.message?.text || '(No text)'}`);
		await next();
	});

	bot.catch((err: any) => {
		console.error('Bot error caught by global handler:', err);
	});
}

/**
 * 注册基础指令处理器
 */
export function registerBasicCommands(bot: any): void {
	// 注册调试处理器
	registerDebugHandlers(bot);

	// 注册基础指令
	bot.command('start', handleStartCommand);
	bot.command('help', handleHelpCommand);

	// 处理未知指令
	bot.on('message:text', async (ctx: Context, next: any) => {
		const text = ctx.message?.text;
		if (
			text?.startsWith('/') &&
			!['start', 'help', 'initialize_group'].some((cmd) => text.startsWith(`/${cmd}`))
		) {
			await handleUnknownCommand(ctx);
			return;
		}
		await next();
	});

	console.log('✅ Basic commands and handlers registered');
}

// 导出工具函数
export { escapeMarkdownV2, getTMAAppUrl };
