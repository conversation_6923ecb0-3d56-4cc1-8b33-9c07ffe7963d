// src/lib/schemas/auth.schema.ts (FINAL REFACTORED VERSION)
import { z } from 'zod';
import {
	telegramUserIdSchema,
	telegramUsernameSchema,
	languageCodeSchema,
	urlSchema
} from '$lib/schemas/common.schema';

// ✅ 1. 创建一个与 Telegram SDK 的 User 接口完全对应的“原始” schema
//    所有字段都使用 snake_case，并补全所有官方定义的字段。
const tmaRawUserSchema = z.object({
	id: z.number(), // 在这里使用 z.number()，因为 telegramUser.id 的校验在别处
	first_name: z.string(),
	last_name: z.string().optional(),
	username: telegramUsernameSchema.optional(),
	language_code: languageCodeSchema.optional(),
	is_premium: z.boolean().optional(),
	photo_url: urlSchema.optional(),
	// 补全字段
	is_bot: z.boolean().optional(),
	added_to_attachment_menu: z.boolean().optional(),
	allows_write_to_pm: z.boolean().optional()
});

// ✅ 2. 创建一个“转换后”的 schema
//    它接收一个 tmaRawUserSchema，验证通过后，将其转换为我们喜欢的 camelCase 格式。
export const telegramUserSchema = tmaRawUserSchema.transform((data) => ({
	id: BigInt(data.id),
	firstName: data.first_name,
	lastName: data.last_name,
	username: data.username,
	languageCode: data.language_code,
	isPremium: data.is_premium,
	photoUrl: data.photo_url,
	isBot: data.is_bot,
	addedToAttachmentMenu: data.added_to_attachment_menu,
	allowsWriteToPm: data.allows_write_to_pm
}));

// ✅ 3. （可选，但推荐）将 launchParamsSchema 重命名为更精确的名字
//    并确保它使用的是我们的“原始” schema，因为它处理的是未经转换的数据。
export const validatedTmaDataSchema = z.object({
	// initDataRaw: z.string().min(20, 'lib_validation_initdata_incomplete'),
	// 我们可以直接从 parse() 后的数据开始验证
	authDate: z.date(),
	hash: z.string(),
	user: tmaRawUserSchema.optional(), // 使用原始 snake_case schema
	startParam: z.string().max(64).optional()
	// ... 其他 initData 字段
});

// --- 类型导出 ---
// ✅ TelegramUserFromSDK 现在会自动推断为 camelCase 格式！
export type TelegramUserFromSDK = z.infer<typeof telegramUserSchema>;
export type ValidatedTmaData = z.infer<typeof validatedTmaDataSchema>;
